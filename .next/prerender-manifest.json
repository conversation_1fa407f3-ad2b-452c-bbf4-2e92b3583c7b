{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc"}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "5648302154a2cdf622478c5452922195", "previewModeSigningKey": "a1fba437d48bae90942a93b2117b026f82736e6b6036920456b1a3c1f9b830d8", "previewModeEncryptionKey": "4e96355fa0344b085e6a14d0130a6ec1fa97f441595b42a2b794d2ba96fa1c89"}}