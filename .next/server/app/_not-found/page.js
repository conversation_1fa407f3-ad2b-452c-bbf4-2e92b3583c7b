(()=>{var e={};e.id=409,e.ids=[409],e.modules={399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},209:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9348:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},412:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},5315:e=>{"use strict";e.exports=require("path")},271:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>u,routeModule:()=>c,tree:()=>l});var n=t(3003),s=t(4293),o=t(6550),i=t.n(o),a=t(6979),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);t.d(r,d);let l=["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.t.bind(t,2075,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,6877)),"/Users/<USER>/Projects/flow/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,2075,23)),"next/dist/client/components/not-found-error"]}],u=[],p={require:t,loadChunk:()=>Promise.resolve()},c=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},9023:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6114,23)),Promise.resolve().then(t.t.bind(t,2639,23)),Promise.resolve().then(t.t.bind(t,9727,23)),Promise.resolve().then(t.t.bind(t,9671,23)),Promise.resolve().then(t.t.bind(t,1868,23)),Promise.resolve().then(t.t.bind(t,4759,23)),Promise.resolve().then(t.t.bind(t,2816,23))},5561:()=>{},6877:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a,metadata:()=>i});var n=t(9351),s=t(5384),o=t.n(s);t(7272);let i={title:"CodeFlow - AI-Powered Ambient Coding Environment",description:"Get into flow state with AI-generated soundscapes and dynamic visuals designed for programmers."};function a({children:e}){return(0,n.jsx)("html",{lang:"en",className:"dark",children:(0,n.jsx)("body",{className:`${o().className} bg-flow-dark text-white antialiased`,children:e})})}},7272:()=>{}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[267,581],()=>t(271));module.exports=n})();