(()=>{var e={};e.id=296,e.ids=[296],e.modules={399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},9348:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},412:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},4601:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>p,serverHooks:()=>l,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>c});var o={};t.r(o),t.d(o,{POST:()=>u});var s=t(2412),n=t(4293),a=t(4147),i=t(7856);async function u(e){try{let{prompt:r}=await e.json();if(!r)return i.NextResponse.json({error:"Prompt is required"},{status:400});console.log("Generating audio for prompt:",r),await new Promise(e=>setTimeout(e,2e3));let t=`/api/mock-audio?prompt=${encodeURIComponent(r)}`;return i.NextResponse.json({audioUrl:t,duration:300,success:!0})}catch(e){return console.error("Audio generation error:",e),i.NextResponse.json({error:"Failed to generate audio"},{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/generate-audio/route",pathname:"/api/generate-audio",filename:"route",bundlePath:"app/api/generate-audio/route"},resolvedPagePath:"/Users/<USER>/Projects/flow/app/api/generate-audio/route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:d,workUnitAsyncStorage:c,serverHooks:l}=p;function x(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:c})}},5303:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[267,814],()=>t(4601));module.exports=o})();