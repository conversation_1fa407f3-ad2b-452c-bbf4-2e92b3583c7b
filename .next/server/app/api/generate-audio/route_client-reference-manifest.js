globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/api/generate-audio/route"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"486":{"*":{"id":"1737","name":"*","chunks":[],"async":false}},"1343":{"*":{"id":"4759","name":"*","chunks":[],"async":false}},"3120":{"*":{"id":"2816","name":"*","chunks":[],"async":false}},"3726":{"*":{"id":"2639","name":"*","chunks":[],"async":false}},"5324":{"*":{"id":"1868","name":"*","chunks":[],"async":false}},"6130":{"*":{"id":"9727","name":"*","chunks":[],"async":false}},"6513":{"*":{"id":"6114","name":"*","chunks":[],"async":false}},"9275":{"*":{"id":"9671","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/Users/<USER>/Projects/flow/node_modules/next/dist/client/components/client-page.js":{"id":6513,"name":"*","chunks":[],"async":false},"/Users/<USER>/Projects/flow/node_modules/next/dist/esm/client/components/client-page.js":{"id":6513,"name":"*","chunks":[],"async":false},"/Users/<USER>/Projects/flow/node_modules/next/dist/client/components/client-segment.js":{"id":3726,"name":"*","chunks":[],"async":false},"/Users/<USER>/Projects/flow/node_modules/next/dist/esm/client/components/client-segment.js":{"id":3726,"name":"*","chunks":[],"async":false},"/Users/<USER>/Projects/flow/node_modules/next/dist/client/components/error-boundary.js":{"id":6130,"name":"*","chunks":[],"async":false},"/Users/<USER>/Projects/flow/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":6130,"name":"*","chunks":[],"async":false},"/Users/<USER>/Projects/flow/node_modules/next/dist/client/components/layout-router.js":{"id":9275,"name":"*","chunks":[],"async":false},"/Users/<USER>/Projects/flow/node_modules/next/dist/esm/client/components/layout-router.js":{"id":9275,"name":"*","chunks":[],"async":false},"/Users/<USER>/Projects/flow/node_modules/next/dist/client/components/not-found-boundary.js":{"id":5324,"name":"*","chunks":[],"async":false},"/Users/<USER>/Projects/flow/node_modules/next/dist/esm/client/components/not-found-boundary.js":{"id":5324,"name":"*","chunks":[],"async":false},"/Users/<USER>/Projects/flow/node_modules/next/dist/client/components/render-from-template-context.js":{"id":1343,"name":"*","chunks":[],"async":false},"/Users/<USER>/Projects/flow/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":1343,"name":"*","chunks":[],"async":false},"/Users/<USER>/Projects/flow/node_modules/next/dist/lib/metadata/metadata-boundary.js":{"id":3120,"name":"*","chunks":[],"async":false},"/Users/<USER>/Projects/flow/node_modules/next/dist/esm/lib/metadata/metadata-boundary.js":{"id":3120,"name":"*","chunks":[],"async":false},"/Users/<USER>/Projects/flow/node_modules/next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":3247,"name":"*","chunks":["185","static/chunks/app/layout-dfa36da6fcb18136.js"],"async":false},"/Users/<USER>/Projects/flow/app/globals.css":{"id":5214,"name":"*","chunks":["185","static/chunks/app/layout-dfa36da6fcb18136.js"],"async":false},"/Users/<USER>/Projects/flow/app/page.tsx":{"id":486,"name":"*","chunks":["305","static/chunks/305-b36de1b161beae9a.js","931","static/chunks/app/page-e9e74de4bfc8b5f6.js"],"async":false}},"entryCSSFiles":{"/Users/<USER>/Projects/flow/":[],"/Users/<USER>/Projects/flow/app/layout":["static/css/85061fec83064e22.css"],"/Users/<USER>/Projects/flow/app/page":[],"/Users/<USER>/Projects/flow/app/api/generate-audio/route":[]},"rscModuleMapping":{"486":{"*":{"id":"9012","name":"*","chunks":[],"async":false}},"1343":{"*":{"id":"8453","name":"*","chunks":[],"async":false}},"3120":{"*":{"id":"3752","name":"*","chunks":[],"async":false}},"3726":{"*":{"id":"4309","name":"*","chunks":[],"async":false}},"5214":{"*":{"id":"7272","name":"*","chunks":[],"async":false}},"5324":{"*":{"id":"2681","name":"*","chunks":[],"async":false}},"6130":{"*":{"id":"6550","name":"*","chunks":[],"async":false}},"6513":{"*":{"id":"5641","name":"*","chunks":[],"async":false}},"9275":{"*":{"id":"7104","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}