(()=>{var e={};e.id=534,e.ids=[534],e.modules={399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},9348:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},412:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},2045:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>p,serverHooks:()=>l,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>c});var o={};t.r(o),t.d(o,{GET:()=>u});var s=t(2412),i=t(4293),n=t(4147),a=t(7856);async function u(e){let{searchParams:r}=new URL(e.url);return r.get("prompt"),a.NextResponse.redirect("https://www.soundjay.com/misc/sounds/bell-ringing-05.wav")}let p=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/mock-audio/route",pathname:"/api/mock-audio",filename:"route",bundlePath:"app/api/mock-audio/route"},resolvedPagePath:"/Users/<USER>/Projects/flow/app/api/mock-audio/route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:d,workUnitAsyncStorage:c,serverHooks:l}=p;function m(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:c})}},5303:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[267,814],()=>t(2045));module.exports=o})();