<!DOCTYPE html><html lang="en" class="dark"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/85061fec83064e22.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-97d9820d9fd40cb1.js"/><script src="/_next/static/chunks/4bd1b696-812da6a5c50fd592.js" async=""></script><script src="/_next/static/chunks/215-aef5e477d06faa90.js" async=""></script><script src="/_next/static/chunks/main-app-99cabc2a961d1788.js" async=""></script><script src="/_next/static/chunks/305-b36de1b161beae9a.js" async=""></script><script src="/_next/static/chunks/app/page-e9e74de4bfc8b5f6.js" async=""></script><meta name="next-size-adjust"/><title>CodeFlow - AI-Powered Ambient Coding Environment</title><meta name="description" content="Get into flow state with AI-generated soundscapes and dynamic visuals designed for programmers."/><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__className_e8ce0c bg-flow-dark text-white antialiased"><main class="relative h-screen w-full overflow-hidden"><div class="absolute inset-0 gradient-bg"></div><div class="relative z-10 h-full flex flex-col"><header class="p-6"><h1 class="text-3xl font-bold text-center mb-8">Code<span class="text-flow-accent">Flow</span></h1></header><div class="flex-1 flex items-center justify-center px-6"><div class="glass-effect p-8 rounded-lg max-w-md w-full" style="opacity:0;transform:translateY(20px) translateZ(0)"><h2 class="text-2xl font-bold text-center mb-6">Generate Your Flow</h2><form class="space-y-4"><div><label for="prompt" class="block text-sm font-medium text-gray-300 mb-2">Describe your ideal coding atmosphere</label><textarea id="prompt" placeholder="e.g., Peaceful rain sounds with soft piano for deep focus..." class="w-full p-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 resize-none" rows="3"></textarea></div><button type="submit" disabled="" class="w-full bg-flow-accent hover:bg-flow-accent/80 disabled:bg-gray-600 disabled:cursor-not-allowed text-white py-3 px-6 rounded-lg font-medium transition-colors">Generate Flow</button></form><div class="mt-6"><p class="text-sm text-gray-400 mb-3">Or try a preset:</p><div class="space-y-2"><button class="w-full text-left p-2 text-sm bg-white/5 hover:bg-white/10 disabled:hover:bg-white/5 rounded border border-white/10 transition-colors">Rainy day coding session</button><button class="w-full text-left p-2 text-sm bg-white/5 hover:bg-white/10 disabled:hover:bg-white/5 rounded border border-white/10 transition-colors">Cyberpunk neon vibes</button><button class="w-full text-left p-2 text-sm bg-white/5 hover:bg-white/10 disabled:hover:bg-white/5 rounded border border-white/10 transition-colors">Forest morning ambience</button><button class="w-full text-left p-2 text-sm bg-white/5 hover:bg-white/10 disabled:hover:bg-white/5 rounded border border-white/10 transition-colors">Retro 8-bit soundscape</button><button class="w-full text-left p-2 text-sm bg-white/5 hover:bg-white/10 disabled:hover:bg-white/5 rounded border border-white/10 transition-colors">Deep focus meditation</button></div></div></div></div></div></main><script src="/_next/static/chunks/webpack-97d9820d9fd40cb1.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"3:\"$Sreact.fragment\"\n4:I[9275,[],\"\"]\n5:I[1343,[],\"\"]\n6:I[6513,[],\"ClientPageRoot\"]\n7:I[486,[\"305\",\"static/chunks/305-b36de1b161beae9a.js\",\"931\",\"static/chunks/app/page-e9e74de4bfc8b5f6.js\"],\"default\",1]\na:I[3120,[],\"OutletBoundary\"]\nc:I[3120,[],\"MetadataBoundary\"]\ne:I[3120,[],\"ViewportBoundary\"]\n10:I[6130,[],\"\"]\n1:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/css/85061fec83064e22.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"6byXWf373TxVgh0QQEVa4\",\"p\":\"\",\"c\":[\"\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"__PAGE__\",{}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$3\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/85061fec83064e22.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"className\":\"dark\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c bg-flow-dark text-white antialiased\",\"children\":[\"$\",\"$L4\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L5\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[]}]}]}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$3\",\"c\",{\"children\":[[\"$\",\"$L6\",null,{\"Component\":\"$7\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@8\",\"$@9\"]}],null,[\"$\",\"$La\",null,{\"children\":\"$Lb\"}]]}],{},null]},null],[\"$\",\"$3\",\"h\",{\"children\":[null,[\"$\",\"$3\",\"cDuofoC9zJcN0AEg3gwXG\",{\"children\":[[\"$\",\"$Lc\",null,{\"children\":\"$Ld\"}],[\"$\",\"$Le\",null,{\"children\":\"$Lf\"}],[\"$\",\"meta\",null,{\"name\":\"next-size-adjust\"}]]}]]}]]],\"m\":\"$undefined\",\"G\":\"$10\",\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"9:{}\n8:{}\n"])</script><script>self.__next_f.push([1,"f:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\nd:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"1\",{\"children\":\"CodeFlow - AI-Powered Ambient Coding Environment\"}],[\"$\",\"meta\",\"2\",{\"name\":\"description\",\"content\":\"Get into flow state with AI-generated soundscapes and dynamic visuals designed for programmers.\"}]]\n"])</script><script>self.__next_f.push([1,"b:null\n"])</script></body></html>