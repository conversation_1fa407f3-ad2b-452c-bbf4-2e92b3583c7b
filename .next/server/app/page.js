(()=>{var t={};t.id=931,t.ids=[931],t.modules={399:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},209:t=>{"use strict";t.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9348:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},412:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},5315:t=>{"use strict";t.exports=require("path")},8823:(t,e,i)=>{"use strict";i.r(e),i.d(e,{GlobalError:()=>o.a,__next_app__:()=>c,pages:()=>h,routeModule:()=>d,tree:()=>u});var r=i(3003),n=i(4293),s=i(6550),o=i.n(s),a=i(6979),l={};for(let t in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(t)&&(l[t]=()=>a[t]);i.d(e,l);let u=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,9012)),"/Users/<USER>/Projects/flow/app/page.tsx"]}]},{layout:[()=>Promise.resolve().then(i.bind(i,6877)),"/Users/<USER>/Projects/flow/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(i.t.bind(i,2075,23)),"next/dist/client/components/not-found-error"]}],h=["/Users/<USER>/Projects/flow/app/page.tsx"],c={require:i,loadChunk:()=>Promise.resolve()},d=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},9628:(t,e,i)=>{Promise.resolve().then(i.bind(i,1737))},9023:(t,e,i)=>{Promise.resolve().then(i.t.bind(i,6114,23)),Promise.resolve().then(i.t.bind(i,2639,23)),Promise.resolve().then(i.t.bind(i,9727,23)),Promise.resolve().then(i.t.bind(i,9671,23)),Promise.resolve().then(i.t.bind(i,1868,23)),Promise.resolve().then(i.t.bind(i,4759,23)),Promise.resolve().then(i.t.bind(i,2816,23))},5561:()=>{},1737:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>n1});var r=i(8819),n=i(7266);let s=(0,n.createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),o=(0,n.createContext)({}),a=(0,n.createContext)(null),l="undefined"!=typeof document,u=l?n.useLayoutEffect:n.useEffect,h=(0,n.createContext)({strict:!1}),c=t=>t.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),d="data-"+c("framerAppearId");function p(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}function m(t){return"string"==typeof t||Array.isArray(t)}function f(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}let g=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],v=["initial",...g];function y(t){return f(t.animate)||v.some(e=>m(t[e]))}function x(t){return!!(y(t)||t.variants)}function b(t){return Array.isArray(t)?t.join(" "):t}let P={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},w={};for(let t in P)w[t]={isEnabled:e=>P[t].some(t=>!!e[t])};let S=(0,n.createContext)({}),T=(0,n.createContext)({}),A=Symbol.for("motionComponentSymbol"),j=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function V(t){if("string"!=typeof t||t.includes("-"));else if(j.indexOf(t)>-1||/[A-Z]/.test(t))return!0;return!1}let C={},M=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],E=new Set(M);function k(t,{layout:e,layoutId:i}){return E.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!C[t]||"opacity"===t)}let D=t=>!!(t&&t.getVelocity),R={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},L=M.length,F=t=>e=>"string"==typeof e&&e.startsWith(t),N=F("--"),B=F("var(--"),O=(t,e)=>e&&"number"==typeof t?e.transform(t):t,U=(t,e,i)=>Math.min(Math.max(i,t),e),I={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},$={...I,transform:t=>U(0,1,t)},W={...I,default:1},z=t=>Math.round(1e5*t)/1e5,H=/(-)?([\d]*\.?[\d])+/g,_=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,G=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function Y(t){return"string"==typeof t}let q=t=>({test:e=>Y(e)&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),X=q("deg"),Z=q("%"),K=q("px"),J=q("vh"),Q=q("vw"),tt={...Z,parse:t=>Z.parse(t)/100,transform:t=>Z.transform(100*t)},te={...I,transform:Math.round},ti={borderWidth:K,borderTopWidth:K,borderRightWidth:K,borderBottomWidth:K,borderLeftWidth:K,borderRadius:K,radius:K,borderTopLeftRadius:K,borderTopRightRadius:K,borderBottomRightRadius:K,borderBottomLeftRadius:K,width:K,maxWidth:K,height:K,maxHeight:K,size:K,top:K,right:K,bottom:K,left:K,padding:K,paddingTop:K,paddingRight:K,paddingBottom:K,paddingLeft:K,margin:K,marginTop:K,marginRight:K,marginBottom:K,marginLeft:K,rotate:X,rotateX:X,rotateY:X,rotateZ:X,scale:W,scaleX:W,scaleY:W,scaleZ:W,skew:X,skewX:X,skewY:X,distance:K,translateX:K,translateY:K,translateZ:K,x:K,y:K,z:K,perspective:K,transformPerspective:K,opacity:$,originX:tt,originY:tt,originZ:K,zIndex:te,fillOpacity:$,strokeOpacity:$,numOctaves:te};function tr(t,e,i,r){let{style:n,vars:s,transform:o,transformOrigin:a}=t,l=!1,u=!1,h=!0;for(let t in e){let i=e[t];if(N(t)){s[t]=i;continue}let r=ti[t],c=O(i,r);if(E.has(t)){if(l=!0,o[t]=c,!h)continue;i!==(r.default||0)&&(h=!1)}else t.startsWith("origin")?(u=!0,a[t]=c):n[t]=c}if(!e.transform&&(l||r?n.transform=function(t,{enableHardwareAcceleration:e=!0,allowTransformNone:i=!0},r,n){let s="";for(let e=0;e<L;e++){let i=M[e];if(void 0!==t[i]){let e=R[i]||i;s+=`${e}(${t[i]}) `}}return e&&!t.z&&(s+="translateZ(0)"),s=s.trim(),n?s=n(t,r?"":s):i&&r&&(s="none"),s}(t.transform,i,h,r):n.transform&&(n.transform="none")),u){let{originX:t="50%",originY:e="50%",originZ:i=0}=a;n.transformOrigin=`${t} ${e} ${i}`}}let tn=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function ts(t,e,i){for(let r in e)D(e[r])||k(r,i)||(t[r]=e[r])}let to=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function ta(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||to.has(t)}let tl=t=>!ta(t);try{!function(t){t&&(tl=e=>e.startsWith("on")?!ta(e):t(e))}(require("@emotion/is-prop-valid").default)}catch(t){}function tu(t,e,i){return"string"==typeof t?t:K.transform(e+i*t)}let th={offset:"stroke-dashoffset",array:"stroke-dasharray"},tc={offset:"strokeDashoffset",array:"strokeDasharray"};function td(t,{attrX:e,attrY:i,attrScale:r,originX:n,originY:s,pathLength:o,pathSpacing:a=1,pathOffset:l=0,...u},h,c,d){if(tr(t,u,h,d),c){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:p,style:m,dimensions:f}=t;p.transform&&(f&&(m.transform=p.transform),delete p.transform),f&&(void 0!==n||void 0!==s||m.transform)&&(m.transformOrigin=function(t,e,i){let r=tu(e,t.x,t.width),n=tu(i,t.y,t.height);return`${r} ${n}`}(f,void 0!==n?n:.5,void 0!==s?s:.5)),void 0!==e&&(p.x=e),void 0!==i&&(p.y=i),void 0!==r&&(p.scale=r),void 0!==o&&function(t,e,i=1,r=0,n=!0){t.pathLength=1;let s=n?th:tc;t[s.offset]=K.transform(-r);let o=K.transform(e),a=K.transform(i);t[s.array]=`${o} ${a}`}(p,o,a,l,!1)}let tp=()=>({...tn(),attrs:{}}),tm=t=>"string"==typeof t&&"svg"===t.toLowerCase();function tf(t,{style:e,vars:i},r,n){for(let s in Object.assign(t.style,e,n&&n.getProjectionStyles(r)),i)t.style.setProperty(s,i[s])}let tg=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function tv(t,e,i,r){for(let i in tf(t,e,void 0,r),e.attrs)t.setAttribute(tg.has(i)?i:c(i),e.attrs[i])}function ty(t,e){let{style:i}=t,r={};for(let n in i)(D(i[n])||e.style&&D(e.style[n])||k(n,t))&&(r[n]=i[n]);return r}function tx(t,e){let i=ty(t,e);for(let r in t)(D(t[r])||D(e[r]))&&(i[-1!==M.indexOf(r)?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r]=t[r]);return i}function tb(t,e,i,r={},n={}){return"function"==typeof e&&(e=e(void 0!==i?i:t.custom,r,n)),"string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e&&(e=e(void 0!==i?i:t.custom,r,n)),e}let tP=t=>Array.isArray(t),tw=t=>!!(t&&"object"==typeof t&&t.mix&&t.toValue),tS=t=>tP(t)?t[t.length-1]||0:t;function tT(t){let e=D(t)?t.get():t;return tw(e)?e.toValue():e}let tA=t=>(e,i)=>{let r=(0,n.useContext)(o),s=(0,n.useContext)(a),l=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e,onMount:i},r,n,s){let o={latestValues:function(t,e,i,r){let n={},s=r(t,{});for(let t in s)n[t]=tT(s[t]);let{initial:o,animate:a}=t,l=y(t),u=x(t);e&&u&&!l&&!1!==t.inherit&&(void 0===o&&(o=e.initial),void 0===a&&(a=e.animate));let h=!!i&&!1===i.initial,c=(h=h||!1===o)?a:o;return c&&"boolean"!=typeof c&&!f(c)&&(Array.isArray(c)?c:[c]).forEach(e=>{let i=tb(t,e);if(!i)return;let{transitionEnd:r,transition:s,...o}=i;for(let t in o){let e=o[t];if(Array.isArray(e)){let t=h?e.length-1:0;e=e[t]}null!==e&&(n[t]=e)}for(let t in r)n[t]=r[t]}),n}(r,n,s,t),renderState:e()};return i&&(o.mount=t=>i(r,t,o)),o})(t,e,r,s);return i?l():function(t){let e=(0,n.useRef)(null);return null===e.current&&(e.current=t()),e.current}(l)},tj=t=>t;class tV{constructor(){this.order=[],this.scheduled=new Set}add(t){if(!this.scheduled.has(t))return this.scheduled.add(t),this.order.push(t),!0}remove(t){let e=this.order.indexOf(t);-1!==e&&(this.order.splice(e,1),this.scheduled.delete(t))}clear(){this.order.length=0,this.scheduled.clear()}}let tC=["prepare","read","update","preRender","render","postRender"],{schedule:tM,cancel:tE,state:tk,steps:tD}=function(t,e){let i=!1,r=!0,n={delta:0,timestamp:0,isProcessing:!1},s=tC.reduce((t,e)=>(t[e]=function(t){let e=new tV,i=new tV,r=0,n=!1,s=!1,o=new WeakSet,a={schedule:(t,s=!1,a=!1)=>{let l=a&&n,u=l?e:i;return s&&o.add(t),u.add(t)&&l&&n&&(r=e.order.length),t},cancel:t=>{i.remove(t),o.delete(t)},process:l=>{if(n){s=!0;return}if(n=!0,[e,i]=[i,e],i.clear(),r=e.order.length)for(let i=0;i<r;i++){let r=e.order[i];r(l),o.has(r)&&(a.schedule(r),t())}n=!1,s&&(s=!1,a.process(l))}};return a}(()=>i=!0),t),{}),o=t=>s[t].process(n),a=()=>{let s=performance.now();i=!1,n.delta=r?1e3/60:Math.max(Math.min(s-n.timestamp,40),1),n.timestamp=s,n.isProcessing=!0,tC.forEach(o),n.isProcessing=!1,i&&e&&(r=!1,t(a))},l=()=>{i=!0,r=!0,n.isProcessing||t(a)};return{schedule:tC.reduce((t,e)=>{let r=s[e];return t[e]=(t,e=!1,n=!1)=>(i||l(),r.schedule(t,e,n)),t},{}),cancel:t=>tC.forEach(e=>s[e].cancel(t)),state:n,steps:s}}("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:tj,!0),tR={useVisualState:tA({scrapeMotionValuesFromProps:tx,createRenderState:tp,onMount:(t,e,{renderState:i,latestValues:r})=>{tM.read(()=>{try{i.dimensions="function"==typeof e.getBBox?e.getBBox():e.getBoundingClientRect()}catch(t){i.dimensions={x:0,y:0,width:0,height:0}}}),tM.render(()=>{td(i,r,{enableHardwareAcceleration:!1},tm(e.tagName),t.transformTemplate),tv(e,i)})}})},tL={useVisualState:tA({scrapeMotionValuesFromProps:ty,createRenderState:tn})};function tF(t,e,i,r={passive:!0}){return t.addEventListener(e,i,r),()=>t.removeEventListener(e,i)}let tN=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function tB(t,e="page"){return{point:{x:t[e+"X"],y:t[e+"Y"]}}}let tO=t=>e=>tN(e)&&t(e,tB(e));function tU(t,e,i,r){return tF(t,e,tO(i),r)}let tI=(t,e)=>i=>e(t(i)),t$=(...t)=>t.reduce(tI);function tW(t){let e=null;return()=>null===e&&(e=t,()=>{e=null})}let tz=tW("dragHorizontal"),tH=tW("dragVertical");function t_(t){let e=!1;if("y"===t)e=tH();else if("x"===t)e=tz();else{let t=tz(),i=tH();t&&i?e=()=>{t(),i()}:(t&&t(),i&&i())}return e}function tG(){let t=t_(!0);return!t||(t(),!1)}class tY{constructor(t){this.isMounted=!1,this.node=t}update(){}}function tq(t,e){let i="onHover"+(e?"Start":"End");return tU(t.current,"pointer"+(e?"enter":"leave"),(r,n)=>{if("touch"===r.pointerType||tG())return;let s=t.getProps();t.animationState&&s.whileHover&&t.animationState.setActive("whileHover",e),s[i]&&tM.update(()=>s[i](r,n))},{passive:!t.getProps()[i]})}class tX extends tY{mount(){this.unmount=t$(tq(this.node,!0),tq(this.node,!1))}unmount(){}}class tZ extends tY{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=t$(tF(this.node.current,"focus",()=>this.onFocus()),tF(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let tK=(t,e)=>!!e&&(t===e||tK(t,e.parentElement));function tJ(t,e){if(!e)return;let i=new PointerEvent("pointer"+t);e(i,tB(i))}class tQ extends tY{constructor(){super(...arguments),this.removeStartListeners=tj,this.removeEndListeners=tj,this.removeAccessibleListeners=tj,this.startPointerPress=(t,e)=>{if(this.isPressing)return;this.removeEndListeners();let i=this.node.getProps(),r=tU(window,"pointerup",(t,e)=>{if(!this.checkPressEnd())return;let{onTap:i,onTapCancel:r,globalTapTarget:n}=this.node.getProps();tM.update(()=>{n||tK(this.node.current,t.target)?i&&i(t,e):r&&r(t,e)})},{passive:!(i.onTap||i.onPointerUp)}),n=tU(window,"pointercancel",(t,e)=>this.cancelPress(t,e),{passive:!(i.onTapCancel||i.onPointerCancel)});this.removeEndListeners=t$(r,n),this.startPress(t,e)},this.startAccessiblePress=()=>{let t=tF(this.node.current,"keydown",t=>{"Enter"!==t.key||this.isPressing||(this.removeEndListeners(),this.removeEndListeners=tF(this.node.current,"keyup",t=>{"Enter"===t.key&&this.checkPressEnd()&&tJ("up",(t,e)=>{let{onTap:i}=this.node.getProps();i&&tM.update(()=>i(t,e))})}),tJ("down",(t,e)=>{this.startPress(t,e)}))}),e=tF(this.node.current,"blur",()=>{this.isPressing&&tJ("cancel",(t,e)=>this.cancelPress(t,e))});this.removeAccessibleListeners=t$(t,e)}}startPress(t,e){this.isPressing=!0;let{onTapStart:i,whileTap:r}=this.node.getProps();r&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),i&&tM.update(()=>i(t,e))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!tG()}cancelPress(t,e){if(!this.checkPressEnd())return;let{onTapCancel:i}=this.node.getProps();i&&tM.update(()=>i(t,e))}mount(){let t=this.node.getProps(),e=tU(t.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(t.onTapStart||t.onPointerStart)}),i=tF(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=t$(e,i)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}let t0=new WeakMap,t1=new WeakMap,t2=t=>{let e=t0.get(t.target);e&&e(t)},t3=t=>{t.forEach(t2)},t5={some:0,all:1};class t4 extends tY{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:r="some",once:n}=t,s={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof r?r:t5[r]};return function(t,e,i){let r=function({root:t,...e}){let i=t||document;t1.has(i)||t1.set(i,{});let r=t1.get(i),n=JSON.stringify(e);return r[n]||(r[n]=new IntersectionObserver(t3,{root:t,...e})),r[n]}(e);return t0.set(t,i),r.observe(t),()=>{t0.delete(t),r.unobserve(t)}}(this.node.current,s,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,n&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:r}=this.node.getProps(),s=e?i:r;s&&s(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}function t6(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let r=0;r<i;r++)if(e[r]!==t[r])return!1;return!0}function t8(t,e,i){let r=t.getProps();return tb(r,e,void 0!==i?i:r.custom,function(t){let e={};return t.values.forEach((t,i)=>e[i]=t.get()),e}(t),function(t){let e={};return t.values.forEach((t,i)=>e[i]=t.getVelocity()),e}(t))}let t9=t=>1e3*t,t7=t=>t/1e3,et={current:!1},ee=t=>Array.isArray(t)&&"number"==typeof t[0],ei=([t,e,i,r])=>`cubic-bezier(${t}, ${e}, ${i}, ${r})`,er={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:ei([0,.65,.55,1]),circOut:ei([.55,0,1,.45]),backIn:ei([.31,.01,.66,-.59]),backOut:ei([.33,1.53,.69,.99])},en=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function es(t,e,i,r){if(t===e&&i===r)return tj;let n=e=>(function(t,e,i,r,n){let s,o;let a=0;do(s=en(o=e+(i-e)/2,r,n)-t)>0?i=o:e=o;while(Math.abs(s)>1e-7&&++a<12);return o})(e,0,1,t,i);return t=>0===t||1===t?t:en(n(t),e,r)}let eo=es(.42,0,1,1),ea=es(0,0,.58,1),el=es(.42,0,.58,1),eu=t=>Array.isArray(t)&&"number"!=typeof t[0],eh=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,ec=t=>e=>1-t(1-e),ed=t=>1-Math.sin(Math.acos(t)),ep=ec(ed),em=eh(ed),ef=es(.33,1.53,.69,.99),eg=ec(ef),ev=eh(eg),ey={linear:tj,easeIn:eo,easeInOut:el,easeOut:ea,circIn:ed,circInOut:em,circOut:ep,backIn:eg,backInOut:ev,backOut:ef,anticipate:t=>(t*=2)<1?.5*eg(t):.5*(2-Math.pow(2,-10*(t-1)))},ex=t=>{if(Array.isArray(t)){tj(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,r,n]=t;return es(e,i,r,n)}return"string"==typeof t?(tj(void 0!==ey[t],`Invalid easing type '${t}'`),ey[t]):t},eb=(t,e)=>i=>!!(Y(i)&&G.test(i)&&i.startsWith(t)||e&&Object.prototype.hasOwnProperty.call(i,e)),eP=(t,e,i)=>r=>{if(!Y(r))return r;let[n,s,o,a]=r.match(H);return{[t]:parseFloat(n),[e]:parseFloat(s),[i]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},ew=t=>U(0,255,t),eS={...I,transform:t=>Math.round(ew(t))},eT={test:eb("rgb","red"),parse:eP("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:r=1})=>"rgba("+eS.transform(t)+", "+eS.transform(e)+", "+eS.transform(i)+", "+z($.transform(r))+")"},eA={test:eb("#"),parse:function(t){let e="",i="",r="",n="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),r=t.substring(5,7),n=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),r=t.substring(3,4),n=t.substring(4,5),e+=e,i+=i,r+=r,n+=n),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(r,16),alpha:n?parseInt(n,16)/255:1}},transform:eT.transform},ej={test:eb("hsl","hue"),parse:eP("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:r=1})=>"hsla("+Math.round(t)+", "+Z.transform(z(e))+", "+Z.transform(z(i))+", "+z($.transform(r))+")"},eV={test:t=>eT.test(t)||eA.test(t)||ej.test(t),parse:t=>eT.test(t)?eT.parse(t):ej.test(t)?ej.parse(t):eA.parse(t),transform:t=>Y(t)?t:t.hasOwnProperty("red")?eT.transform(t):ej.transform(t)},eC=(t,e,i)=>-i*t+i*e+t;function eM(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}let eE=(t,e,i)=>{let r=t*t;return Math.sqrt(Math.max(0,i*(e*e-r)+r))},ek=[eA,eT,ej],eD=t=>ek.find(e=>e.test(t));function eR(t){let e=eD(t);tj(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`);let i=e.parse(t);return e===ej&&(i=function({hue:t,saturation:e,lightness:i,alpha:r}){t/=360,i/=100;let n=0,s=0,o=0;if(e/=100){let r=i<.5?i*(1+e):i+e-i*e,a=2*i-r;n=eM(a,r,t+1/3),s=eM(a,r,t),o=eM(a,r,t-1/3)}else n=s=o=i;return{red:Math.round(255*n),green:Math.round(255*s),blue:Math.round(255*o),alpha:r}}(i)),i}let eL=(t,e)=>{let i=eR(t),r=eR(e),n={...i};return t=>(n.red=eE(i.red,r.red,t),n.green=eE(i.green,r.green,t),n.blue=eE(i.blue,r.blue,t),n.alpha=eC(i.alpha,r.alpha,t),eT.transform(n))},eF={regex:/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,countKey:"Vars",token:"${v}",parse:tj},eN={regex:_,countKey:"Colors",token:"${c}",parse:eV.parse},eB={regex:H,countKey:"Numbers",token:"${n}",parse:I.parse};function eO(t,{regex:e,countKey:i,token:r,parse:n}){let s=t.tokenised.match(e);s&&(t["num"+i]=s.length,t.tokenised=t.tokenised.replace(e,r),t.values.push(...s.map(n)))}function eU(t){let e=t.toString(),i={value:e,tokenised:e,values:[],numVars:0,numColors:0,numNumbers:0};return i.value.includes("var(--")&&eO(i,eF),eO(i,eN),eO(i,eB),i}function eI(t){return eU(t).values}function e$(t){let{values:e,numColors:i,numVars:r,tokenised:n}=eU(t),s=e.length;return t=>{let e=n;for(let n=0;n<s;n++)e=n<r?e.replace(eF.token,t[n]):n<r+i?e.replace(eN.token,eV.transform(t[n])):e.replace(eB.token,z(t[n]));return e}}let eW=t=>"number"==typeof t?0:t,ez={test:function(t){var e,i;return isNaN(t)&&Y(t)&&((null===(e=t.match(H))||void 0===e?void 0:e.length)||0)+((null===(i=t.match(_))||void 0===i?void 0:i.length)||0)>0},parse:eI,createTransformer:e$,getAnimatableNone:function(t){let e=eI(t);return e$(t)(e.map(eW))}},eH=(t,e)=>i=>`${i>0?e:t}`;function e_(t,e){return"number"==typeof t?i=>eC(t,e,i):eV.test(t)?eL(t,e):t.startsWith("var(")?eH(t,e):eq(t,e)}let eG=(t,e)=>{let i=[...t],r=i.length,n=t.map((t,i)=>e_(t,e[i]));return t=>{for(let e=0;e<r;e++)i[e]=n[e](t);return i}},eY=(t,e)=>{let i={...t,...e},r={};for(let n in i)void 0!==t[n]&&void 0!==e[n]&&(r[n]=e_(t[n],e[n]));return t=>{for(let e in r)i[e]=r[e](t);return i}},eq=(t,e)=>{let i=ez.createTransformer(e),r=eU(t),n=eU(e);return r.numVars===n.numVars&&r.numColors===n.numColors&&r.numNumbers>=n.numNumbers?t$(eG(r.values,n.values),i):(tj(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),eH(t,e))},eX=(t,e,i)=>{let r=e-t;return 0===r?1:(i-t)/r},eZ=(t,e)=>i=>eC(t,e,i);function eK(t,e,{clamp:i=!0,ease:r,mixer:n}={}){let s=t.length;if(tj(s===e.length,"Both input and output ranges must be the same length"),1===s)return()=>e[0];t[0]>t[s-1]&&(t=[...t].reverse(),e=[...e].reverse());let o=function(t,e,i){let r=[],n=i||function(t){if("number"==typeof t);else if("string"==typeof t)return eV.test(t)?eL:eq;else if(Array.isArray(t))return eG;else if("object"==typeof t)return eY;return eZ}(t[0]),s=t.length-1;for(let i=0;i<s;i++){let s=n(t[i],t[i+1]);e&&(s=t$(Array.isArray(e)?e[i]||tj:e,s)),r.push(s)}return r}(e,r,n),a=o.length,l=e=>{let i=0;if(a>1)for(;i<t.length-2&&!(e<t[i+1]);i++);let r=eX(t[i],t[i+1],e);return o[i](r)};return i?e=>l(U(t[0],t[s-1],e)):l}function eJ({duration:t=300,keyframes:e,times:i,ease:r="easeInOut"}){let n=eu(r)?r.map(ex):ex(r),s={done:!1,value:e[0]},o=eK((i&&i.length===e.length?i:function(t){let e=[0];return function(t,e){let i=t[t.length-1];for(let r=1;r<=e;r++){let n=eX(0,e,r);t.push(eC(i,1,n))}}(e,t.length-1),e}(e)).map(e=>e*t),e,{ease:Array.isArray(n)?n:e.map(()=>n||el).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(s.value=o(e),s.done=e>=t,s)}}function eQ(t,e,i){var r,n;let s=Math.max(e-5,0);return r=i-t(s),(n=e-s)?1e3/n*r:0}function e0(t,e){return t*Math.sqrt(1-e*e)}let e1=["duration","bounce"],e2=["stiffness","damping","mass"];function e3(t,e){return e.some(e=>void 0!==t[e])}function e5({keyframes:t,restDelta:e,restSpeed:i,...r}){let n;let s=t[0],o=t[t.length-1],a={done:!1,value:s},{stiffness:l,damping:u,mass:h,duration:c,velocity:d,isResolvedFromDuration:p}=function(t){let e={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...t};if(!e3(t,e2)&&e3(t,e1)){let i=function({duration:t=800,bounce:e=.25,velocity:i=0,mass:r=1}){let n,s;tj(t<=t9(10),"Spring duration must be 10 seconds or less");let o=1-e;o=U(.05,1,o),t=U(.01,10,t7(t)),o<1?(n=e=>{let r=e*o,n=r*t;return .001-(r-i)/e0(e,o)*Math.exp(-n)},s=e=>{let r=e*o*t,s=Math.pow(o,2)*Math.pow(e,2)*t,a=Math.exp(-r),l=e0(Math.pow(e,2),o);return(r*i+i-s)*a*(-n(e)+.001>0?-1:1)/l}):(n=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),s=e=>t*t*(i-e)*Math.exp(-e*t));let a=function(t,e,i){let r=i;for(let i=1;i<12;i++)r-=t(r)/e(r);return r}(n,s,5/t);if(t=t9(t),isNaN(a))return{stiffness:100,damping:10,duration:t};{let e=Math.pow(a,2)*r;return{stiffness:e,damping:2*o*Math.sqrt(r*e),duration:t}}}(t);(e={...e,...i,mass:1}).isResolvedFromDuration=!0}return e}({...r,velocity:-t7(r.velocity||0)}),m=d||0,f=u/(2*Math.sqrt(l*h)),g=o-s,v=t7(Math.sqrt(l/h)),y=5>Math.abs(g);if(i||(i=y?.01:2),e||(e=y?.005:.5),f<1){let t=e0(v,f);n=e=>o-Math.exp(-f*v*e)*((m+f*v*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}else if(1===f)n=t=>o-Math.exp(-v*t)*(g+(m+v*g)*t);else{let t=v*Math.sqrt(f*f-1);n=e=>{let i=Math.exp(-f*v*e),r=Math.min(t*e,300);return o-i*((m+f*v*g)*Math.sinh(r)+t*g*Math.cosh(r))/t}}return{calculatedDuration:p&&c||null,next:t=>{let r=n(t);if(p)a.done=t>=c;else{let s=m;0!==t&&(s=f<1?eQ(n,t,r):0);let l=Math.abs(s)<=i,u=Math.abs(o-r)<=e;a.done=l&&u}return a.value=a.done?o:r,a}}}function e4({keyframes:t,velocity:e=0,power:i=.8,timeConstant:r=325,bounceDamping:n=10,bounceStiffness:s=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:h}){let c,d;let p=t[0],m={done:!1,value:p},f=t=>void 0!==a&&t<a||void 0!==l&&t>l,g=t=>void 0===a?l:void 0===l?a:Math.abs(a-t)<Math.abs(l-t)?a:l,v=i*e,y=p+v,x=void 0===o?y:o(y);x!==y&&(v=x-p);let b=t=>-v*Math.exp(-t/r),P=t=>x+b(t),w=t=>{let e=b(t),i=P(t);m.done=Math.abs(e)<=u,m.value=m.done?x:i},S=t=>{f(m.value)&&(c=t,d=e5({keyframes:[m.value,g(m.value)],velocity:eQ(P,t,m.value),damping:n,stiffness:s,restDelta:u,restSpeed:h}))};return S(0),{calculatedDuration:null,next:t=>{let e=!1;return(d||void 0!==c||(e=!0,w(t),S(t)),void 0!==c&&t>c)?d.next(t-c):(e||w(t),m)}}}let e6=t=>{let e=({timestamp:e})=>t(e);return{start:()=>tM.update(e,!0),stop:()=>tE(e),now:()=>tk.isProcessing?tk.timestamp:performance.now()}};function e8(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}let e9={decay:e4,inertia:e4,tween:eJ,keyframes:eJ,spring:e5};function e7({autoplay:t=!0,delay:e=0,driver:i=e6,keyframes:r,type:n="keyframes",repeat:s=0,repeatDelay:o=0,repeatType:a="loop",onPlay:l,onStop:u,onComplete:h,onUpdate:c,...d}){let p,m,f,g,v,y=1,x=!1,b=()=>{m=new Promise(t=>{p=t})};b();let P=e9[n]||eJ;P!==eJ&&"number"!=typeof r[0]&&(g=eK([0,100],r,{clamp:!1}),r=[0,100]);let w=P({...d,keyframes:r});"mirror"===a&&(v=P({...d,keyframes:[...r].reverse(),velocity:-(d.velocity||0)}));let S="idle",T=null,A=null,j=null;null===w.calculatedDuration&&s&&(w.calculatedDuration=e8(w));let{calculatedDuration:V}=w,C=1/0,M=1/0;null!==V&&(M=(C=V+o)*(s+1)-o);let E=0,k=t=>{if(null===A)return;y>0&&(A=Math.min(A,t)),y<0&&(A=Math.min(t-M/y,A));let i=(E=null!==T?T:Math.round(t-A)*y)-e*(y>=0?1:-1),n=y>=0?i<0:i>M;E=Math.max(i,0),"finished"===S&&null===T&&(E=M);let l=E,u=w;if(s){let t=Math.min(E,M)/C,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,s+1))%2&&("reverse"===a?(i=1-i,o&&(i-=o/C)):"mirror"===a&&(u=v)),l=U(0,1,i)*C}let h=n?{done:!1,value:r[0]}:u.next(l);g&&(h.value=g(h.value));let{done:d}=h;n||null===V||(d=y>=0?E>=M:E<=0);let p=null===T&&("finished"===S||"running"===S&&d);return c&&c(h.value),p&&L(),h},D=()=>{f&&f.stop(),f=void 0},R=()=>{S="idle",D(),p(),b(),A=j=null},L=()=>{S="finished",h&&h(),D(),p()},F=()=>{if(x)return;f||(f=i(k));let t=f.now();l&&l(),null!==T?A=t-T:A&&"finished"!==S||(A=t),"finished"===S&&b(),j=A,T=null,S="running",f.start()};t&&F();let N={then:(t,e)=>m.then(t,e),get time(){return t7(E)},set time(newTime){E=newTime=t9(newTime),null===T&&f&&0!==y?A=f.now()-newTime/y:T=newTime},get duration(){return t7(null===w.calculatedDuration?e8(w):w.calculatedDuration)},get speed(){return y},set speed(newSpeed){if(newSpeed===y||!f)return;y=newSpeed,N.time=t7(E)},get state(){return S},play:F,pause:()=>{S="paused",T=E},stop:()=>{x=!0,"idle"!==S&&(S="idle",u&&u(),R())},cancel:()=>{null!==j&&k(j),R()},complete:()=>{S="finished"},sample:t=>(A=0,k(t))};return N}let it=function(t){let e;return()=>(void 0===e&&(e=t()),e)}(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),ie=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),ii=(t,e)=>"spring"===e.type||"backgroundColor"===t||!function t(e){return!!(!e||"string"==typeof e&&er[e]||ee(e)||Array.isArray(e)&&e.every(t))}(e.ease),ir={type:"spring",stiffness:500,damping:25,restSpeed:10},is=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),io={type:"keyframes",duration:.8},ia={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},il=(t,{keyframes:e})=>e.length>2?io:E.has(t)?t.startsWith("scale")?is(e[1]):ir:ia,iu=(t,e)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(ez.test(e)||"0"===e)&&!e.startsWith("url(")),ih=new Set(["brightness","contrast","saturate","opacity"]);function ic(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[r]=i.match(H)||[];if(!r)return t;let n=i.replace(r,""),s=ih.has(e)?1:0;return r!==i&&(s*=100),e+"("+s+n+")"}let id=/([a-z-]*)\(.*?\)/g,ip={...ez,getAnimatableNone:t=>{let e=t.match(id);return e?e.map(ic).join(" "):t}},im={...ti,color:eV,backgroundColor:eV,outlineColor:eV,fill:eV,stroke:eV,borderColor:eV,borderTopColor:eV,borderRightColor:eV,borderBottomColor:eV,borderLeftColor:eV,filter:ip,WebkitFilter:ip},ig=t=>im[t];function iv(t,e){let i=ig(t);return i!==ip&&(i=ez),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let iy=t=>/^0[^.\s]+$/.test(t);function ix(t,e){return t[e]||t.default||t}let ib={skipAnimations:!1},iP=(t,e,i,r={})=>n=>{let s=ix(r,t)||{},o=s.delay||r.delay||0,{elapsed:a=0}=r;a-=t9(o);let l=function(t,e,i,r){let n,s;let o=iu(e,i);n=Array.isArray(i)?[...i]:[null,i];let a=void 0!==r.from?r.from:t.get(),l=[];for(let t=0;t<n.length;t++){var u;null===n[t]&&(n[t]=0===t?a:n[t-1]),("number"==typeof(u=n[t])?0===u:null!==u?"none"===u||"0"===u||iy(u):void 0)&&l.push(t),"string"==typeof n[t]&&"none"!==n[t]&&"0"!==n[t]&&(s=n[t])}if(o&&l.length&&s)for(let t=0;t<l.length;t++)n[l[t]]=iv(e,s);return n}(e,t,i,s),u=l[0],h=l[l.length-1],c=iu(t,u),d=iu(t,h);tj(c===d,`You are trying to animate ${t} from "${u}" to "${h}". ${u} is not an animatable value - to enable this animation set ${u} to a value animatable to ${h} via the \`style\` property.`);let p={keyframes:l,velocity:e.getVelocity(),ease:"easeOut",...s,delay:-a,onUpdate:t=>{e.set(t),s.onUpdate&&s.onUpdate(t)},onComplete:()=>{n(),s.onComplete&&s.onComplete()}};if(!function({when:t,delay:e,delayChildren:i,staggerChildren:r,staggerDirection:n,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(s)&&(p={...p,...il(t,p)}),p.duration&&(p.duration=t9(p.duration)),p.repeatDelay&&(p.repeatDelay=t9(p.repeatDelay)),!c||!d||et.current||!1===s.type||ib.skipAnimations)return function({keyframes:t,delay:e,onUpdate:i,onComplete:r}){let n=()=>(i&&i(t[t.length-1]),r&&r(),{time:0,speed:1,duration:0,play:tj,pause:tj,stop:tj,then:t=>(t(),Promise.resolve()),cancel:tj,complete:tj});return e?e7({keyframes:[0,1],duration:0,delay:e,onComplete:n}):n()}(et.current?{...p,delay:0}:p);if(!r.isHandoff&&e.owner&&e.owner.current instanceof HTMLElement&&!e.owner.getProps().onUpdate){let i=function(t,e,{onUpdate:i,onComplete:r,...n}){let s,o;if(!(it()&&ie.has(e)&&!n.repeatDelay&&"mirror"!==n.repeatType&&0!==n.damping&&"inertia"!==n.type))return!1;let a=!1,l=!1,u=()=>{o=new Promise(t=>{s=t})};u();let{keyframes:h,duration:c=300,ease:d,times:p}=n;if(ii(e,n)){let t=e7({...n,repeat:0,delay:0}),e={done:!1,value:h[0]},i=[],r=0;for(;!e.done&&r<2e4;)e=t.sample(r),i.push(e.value),r+=10;p=void 0,h=i,c=r-10,d="linear"}let m=function(t,e,i,{delay:r=0,duration:n,repeat:s=0,repeatType:o="loop",ease:a,times:l}={}){let u={[e]:i};l&&(u.offset=l);let h=function t(e){if(e)return ee(e)?ei(e):Array.isArray(e)?e.map(t):er[e]}(a);return Array.isArray(h)&&(u.easing=h),t.animate(u,{delay:r,duration:n,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:s+1,direction:"reverse"===o?"alternate":"normal"})}(t.owner.current,e,h,{...n,duration:c,ease:d,times:p}),f=()=>{l=!1,m.cancel()},g=()=>{l=!0,tM.update(f),s(),u()};return m.onfinish=()=>{l||(t.set(function(t,{repeat:e,repeatType:i="loop"}){let r=e&&"loop"!==i&&e%2==1?0:t.length-1;return t[r]}(h,n)),r&&r(),g())},{then:(t,e)=>o.then(t,e),attachTimeline:t=>(m.timeline=t,m.onfinish=null,tj),get time(){return t7(m.currentTime||0)},set time(newTime){m.currentTime=t9(newTime)},get speed(){return m.playbackRate},set speed(newSpeed){m.playbackRate=newSpeed},get duration(){return t7(c)},play:()=>{a||(m.play(),tE(f))},pause:()=>m.pause(),stop:()=>{if(a=!0,"idle"===m.playState)return;let{currentTime:e}=m;if(e){let i=e7({...n,autoplay:!1});t.setWithVelocity(i.sample(e-10).value,i.sample(e).value,10)}g()},complete:()=>{l||m.finish()},cancel:g}}(e,t,p);if(i)return i}return e7(p)};function iw(t){return!!(D(t)&&t.add)}let iS=t=>/^\-?\d*\.?\d+$/.test(t);function iT(t,e){-1===t.indexOf(e)&&t.push(e)}function iA(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class ij{constructor(){this.subscriptions=[]}add(t){return iT(this.subscriptions,t),()=>iA(this.subscriptions,t)}notify(t,e,i){let r=this.subscriptions.length;if(r){if(1===r)this.subscriptions[0](t,e,i);else for(let n=0;n<r;n++){let r=this.subscriptions[n];r&&r(t,e,i)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let iV=t=>!isNaN(parseFloat(t)),iC={current:void 0};class iM{constructor(t,e={}){this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(t,e=!0)=>{this.prev=this.current,this.current=t;let{delta:i,timestamp:r}=tk;this.lastUpdated!==r&&(this.timeDelta=i,this.lastUpdated=r,tM.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),e&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>tM.postRender(this.velocityCheck),this.velocityCheck=({timestamp:t})=>{t!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=t,this.canTrackVelocity=iV(this.current),this.owner=e.owner}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new ij);let i=this.events[t].add(e);return"change"===t?()=>{i(),tM.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=t,this.timeDelta=i}jump(t){this.updateAndNotify(t),this.prev=t,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return iC.current&&iC.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t,e;return this.canTrackVelocity?(t=parseFloat(this.current)-parseFloat(this.prev),(e=this.timeDelta)?1e3/e*t:0):0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function iE(t,e){return new iM(t,e)}let ik=t=>e=>e.test(t),iD=[I,K,Z,X,Q,J,{test:t=>"auto"===t,parse:t=>t}],iR=t=>iD.find(ik(t)),iL=[...iD,eV,ez],iF=t=>iL.find(ik(t));function iN(t,e,{delay:i=0,transitionOverride:r,type:n}={}){let{transition:s=t.getDefaultTransition(),transitionEnd:o,...a}=t.makeTargetAnimatable(e),l=t.getValue("willChange");r&&(s=r);let u=[],h=n&&t.animationState&&t.animationState.getState()[n];for(let e in a){let r=t.getValue(e),n=a[e];if(!r||void 0===n||h&&function({protectedKeys:t,needsAnimating:e},i){let r=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,r}(h,e))continue;let o={delay:i,elapsed:0,...ix(s||{},e)};if(window.HandoffAppearAnimations){let i=t.getProps()[d];if(i){let t=window.HandoffAppearAnimations(i,e,r,tM);null!==t&&(o.elapsed=t,o.isHandoff=!0)}}let c=!o.isHandoff&&!function(t,e){let i=t.get();if(!Array.isArray(e))return i!==e;for(let t=0;t<e.length;t++)if(e[t]!==i)return!0}(r,n);if("spring"===o.type&&(r.getVelocity()||o.velocity)&&(c=!1),r.animation&&(c=!1),c)continue;r.start(iP(e,r,n,t.shouldReduceMotion&&E.has(e)?{type:!1}:o));let p=r.animation;iw(l)&&(l.add(e),p.then(()=>l.remove(e))),u.push(p)}return o&&Promise.all(u).then(()=>{o&&function(t,e){let i=t8(t,e),{transitionEnd:r={},transition:n={},...s}=i?t.makeTargetAnimatable(i,!1):{};for(let e in s={...s,...r}){let i=tS(s[e]);t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,iE(i))}}(t,o)}),u}function iB(t,e,i={}){let r=t8(t,e,i.custom),{transition:n=t.getDefaultTransition()||{}}=r||{};i.transitionOverride&&(n=i.transitionOverride);let s=r?()=>Promise.all(iN(t,r,i)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(r=0)=>{let{delayChildren:s=0,staggerChildren:o,staggerDirection:a}=n;return function(t,e,i=0,r=0,n=1,s){let o=[],a=(t.variantChildren.size-1)*r,l=1===n?(t=0)=>t*r:(t=0)=>a-t*r;return Array.from(t.variantChildren).sort(iO).forEach((t,r)=>{t.notify("AnimationStart",e),o.push(iB(t,e,{...s,delay:i+l(r)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,s+r,o,a,i)}:()=>Promise.resolve(),{when:a}=n;if(!a)return Promise.all([s(),o(i.delay)]);{let[t,e]="beforeChildren"===a?[s,o]:[o,s];return t().then(()=>e())}}function iO(t,e){return t.sortNodePosition(e)}let iU=[...g].reverse(),iI=g.length;function i$(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}class iW extends tY{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let r;if(t.notify("AnimationStart",e),Array.isArray(e))r=Promise.all(e.map(e=>iB(t,e,i)));else if("string"==typeof e)r=iB(t,e,i);else{let n="function"==typeof e?t8(t,e,i.custom):e;r=Promise.all(iN(t,n,i))}return r.then(()=>t.notify("AnimationComplete",e))})(t,e,i))),i={animate:i$(!0),whileInView:i$(),whileHover:i$(),whileTap:i$(),whileDrag:i$(),whileFocus:i$(),exit:i$()},r=!0,n=(e,i)=>{let r=t8(t,i);if(r){let{transition:t,transitionEnd:i,...n}=r;e={...e,...n,...i}}return e};function s(s,o){let a=t.getProps(),l=t.getVariantContext(!0)||{},u=[],h=new Set,c={},d=1/0;for(let e=0;e<iI;e++){var p;let g=iU[e],v=i[g],y=void 0!==a[g]?a[g]:l[g],x=m(y),b=g===o?v.isActive:null;!1===b&&(d=e);let P=y===l[g]&&y!==a[g]&&x;if(P&&r&&t.manuallyAnimateOnMount&&(P=!1),v.protectedKeys={...c},!v.isActive&&null===b||!y&&!v.prevProp||f(y)||"boolean"==typeof y)continue;let w=(p=v.prevProp,("string"==typeof y?y!==p:!!Array.isArray(y)&&!t6(y,p))||g===o&&v.isActive&&!P&&x||e>d&&x),S=!1,T=Array.isArray(y)?y:[y],A=T.reduce(n,{});!1===b&&(A={});let{prevResolvedValues:j={}}=v,V={...j,...A},C=t=>{w=!0,h.has(t)&&(S=!0,h.delete(t)),v.needsAnimating[t]=!0};for(let t in V){let e=A[t],i=j[t];if(!c.hasOwnProperty(t))(tP(e)&&tP(i)?t6(e,i):e===i)?void 0!==e&&h.has(t)?C(t):v.protectedKeys[t]=!0:void 0!==e?C(t):h.add(t)}v.prevProp=y,v.prevResolvedValues=A,v.isActive&&(c={...c,...A}),r&&t.blockInitialAnimation&&(w=!1),w&&(!P||S)&&u.push(...T.map(t=>({animation:t,options:{type:g,...s}})))}if(h.size){let e={};h.forEach(i=>{let r=t.getBaseTarget(i);void 0!==r&&(e[i]=r)}),u.push({animation:e})}let g=!!u.length;return r&&(!1===a.initial||a.initial===a.animate)&&!t.manuallyAnimateOnMount&&(g=!1),r=!1,g?e(u):Promise.resolve()}return{animateChanges:s,setActive:function(e,r,n){var o;if(i[e].isActive===r)return Promise.resolve();null===(o=t.variantChildren)||void 0===o||o.forEach(t=>{var i;return null===(i=t.animationState)||void 0===i?void 0:i.setActive(e,r)}),i[e].isActive=r;let a=s(n,e);for(let t in i)i[t].protectedKeys={};return a},setAnimateFunction:function(i){e=i(t)},getState:()=>i}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();this.unmount(),f(t)&&(this.unmount=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){}}let iz=0;class iH extends tY{constructor(){super(...arguments),this.id=iz++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e,custom:i}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===r)return;let n=this.node.animationState.setActive("exit",!t,{custom:null!=i?i:this.node.getProps().custom});e&&!t&&n.then(()=>e(this.id))}mount(){let{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}let i_=(t,e)=>Math.abs(t-e);class iG{constructor(t,e,{transformPagePoint:i,contextWindow:r,dragSnapToOrigin:n=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=iX(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(i_(t.x,e.x)**2+i_(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:r}=t,{timestamp:n}=tk;this.history.push({...r,timestamp:n});let{onStart:s,onMove:o}=this.handlers;e||(s&&s(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=iY(e,this.transformPagePoint),tM.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:r,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=iX("pointercancel"===t.type?this.lastMoveEventInfo:iY(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,s),r&&r(t,s)},!tN(t))return;this.dragSnapToOrigin=n,this.handlers=e,this.transformPagePoint=i,this.contextWindow=r||window;let s=iY(tB(t),this.transformPagePoint),{point:o}=s,{timestamp:a}=tk;this.history=[{...o,timestamp:a}];let{onSessionStart:l}=e;l&&l(t,iX(s,this.history)),this.removeListeners=t$(tU(this.contextWindow,"pointermove",this.handlePointerMove),tU(this.contextWindow,"pointerup",this.handlePointerUp),tU(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),tE(this.updatePoint)}}function iY(t,e){return e?{point:e(t.point)}:t}function iq(t,e){return{x:t.x-e.x,y:t.y-e.y}}function iX({point:t},e){return{point:t,delta:iq(t,iZ(e)),offset:iq(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,r=null,n=iZ(t);for(;i>=0&&(r=t[i],!(n.timestamp-r.timestamp>t9(.1)));)i--;if(!r)return{x:0,y:0};let s=t7(n.timestamp-r.timestamp);if(0===s)return{x:0,y:0};let o={x:(n.x-r.x)/s,y:(n.y-r.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,0)}}function iZ(t){return t[t.length-1]}function iK(t){return t.max-t.min}function iJ(t,e=0,i=.01){return Math.abs(t-e)<=i}function iQ(t,e,i,r=.5){t.origin=r,t.originPoint=eC(e.min,e.max,t.origin),t.scale=iK(i)/iK(e),(iJ(t.scale,1,1e-4)||isNaN(t.scale))&&(t.scale=1),t.translate=eC(i.min,i.max,t.origin)-t.originPoint,(iJ(t.translate)||isNaN(t.translate))&&(t.translate=0)}function i0(t,e,i,r){iQ(t.x,e.x,i.x,r?r.originX:void 0),iQ(t.y,e.y,i.y,r?r.originY:void 0)}function i1(t,e,i){t.min=i.min+e.min,t.max=t.min+iK(e)}function i2(t,e,i){t.min=e.min-i.min,t.max=t.min+iK(e)}function i3(t,e,i){i2(t.x,e.x,i.x),i2(t.y,e.y,i.y)}function i5(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function i4(t,e){let i=e.min-t.min,r=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,r]=[r,i]),{min:i,max:r}}function i6(t,e,i){return{min:i8(t,e),max:i8(t,i)}}function i8(t,e){return"number"==typeof t?t:t[e]||0}let i9=()=>({translate:0,scale:1,origin:0,originPoint:0}),i7=()=>({x:i9(),y:i9()}),rt=()=>({min:0,max:0}),re=()=>({x:rt(),y:rt()});function ri(t){return[t("x"),t("y")]}function rr({top:t,left:e,right:i,bottom:r}){return{x:{min:e,max:i},y:{min:t,max:r}}}function rn(t){return void 0===t||1===t}function rs({scale:t,scaleX:e,scaleY:i}){return!rn(t)||!rn(e)||!rn(i)}function ro(t){return rs(t)||ra(t)||t.z||t.rotate||t.rotateX||t.rotateY}function ra(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function rl(t,e,i,r,n){return void 0!==n&&(t=r+n*(t-r)),r+i*(t-r)+e}function ru(t,e=0,i=1,r,n){t.min=rl(t.min,e,i,r,n),t.max=rl(t.max,e,i,r,n)}function rh(t,{x:e,y:i}){ru(t.x,e.translate,e.scale,e.originPoint),ru(t.y,i.translate,i.scale,i.originPoint)}function rc(t){return Number.isInteger(t)?t:t>1.0000000000001||t<.999999999999?t:1}function rd(t,e){t.min=t.min+e,t.max=t.max+e}function rp(t,e,[i,r,n]){let s=void 0!==e[n]?e[n]:.5,o=eC(t.min,t.max,s);ru(t,e[i],e[r],o,e.scale)}let rm=["x","scaleX","originX"],rf=["y","scaleY","originY"];function rg(t,e){rp(t.x,e,rm),rp(t.y,e,rf)}function rv(t,e){return rr(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),r=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:r.y,right:r.x}}(t.getBoundingClientRect(),e))}let ry=({current:t})=>t?t.ownerDocument.defaultView:null,rx=new WeakMap;class rb{constructor(t){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=re(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:r}=this.getProps();this.panSession=new iG(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(tB(t,"page").point)},onStart:(t,e)=>{let{drag:i,dragPropagation:r,onDragStart:n}=this.getProps();if(i&&!r&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=t_(i),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),ri(t=>{let e=this.getAxisMotionValue(t).get()||0;if(Z.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let r=i.layout.layoutBox[t];r&&(e=iK(r)*(parseFloat(e)/100))}}this.originPoint[t]=e}),n&&tM.update(()=>n(t,e),!1,!0);let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:r,onDirectionLock:n,onDrag:s}=this.getProps();if(!i&&!this.openGlobalLock)return;let{offset:o}=e;if(r&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),s&&s(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>ri(t=>{var e;return"paused"===this.getAnimationState(t)&&(null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r,contextWindow:ry(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:r}=e;this.startAnimation(r);let{onDragEnd:n}=this.getProps();n&&tM.update(()=>n(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:r}=this.getProps();if(!i||!rP(t,r,this.currentDirection))return;let n=this.getAxisMotionValue(t),s=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(s=function(t,{min:e,max:i},r){return void 0!==e&&t<e?t=r?eC(e,t,r.min):Math.max(t,e):void 0!==i&&t>i&&(t=r?eC(i,t,r.max):Math.min(t,i)),t}(s,this.constraints[t],this.elastic[t])),n.set(s)}resolveConstraints(){var t;let{dragConstraints:e,dragElastic:i}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(t=this.visualElement.projection)||void 0===t?void 0:t.layout,n=this.constraints;e&&p(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&r?this.constraints=function(t,{top:e,left:i,bottom:r,right:n}){return{x:i5(t.x,i,n),y:i5(t.y,e,r)}}(r.layoutBox,e):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:i6(t,"left","right"),y:i6(t,"top","bottom")}}(i),n!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&ri(t=>{this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(r.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!p(e))return!1;let r=e.current;tj(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let s=function(t,e,i){let r=rv(t,i),{scroll:n}=e;return n&&(rd(r.x,n.offset.x),rd(r.y,n.offset.y)),r}(r,n.root,this.visualElement.getTransformPagePoint()),o={x:i4((t=n.layout.layoutBox).x,s.x),y:i4(t.y,s.y)};if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=rr(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:r,dragTransition:n,dragSnapToOrigin:s,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(ri(o=>{if(!rP(o,e,this.currentDirection))return;let l=a&&a[o]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[o]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return i.start(iP(t,i,0,e))}stopAnimation(){ri(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){ri(t=>{var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.pause()})}getAnimationState(t){var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.state}getAxisMotionValue(t){let e="_drag"+t.toUpperCase(),i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){ri(e=>{let{drag:i}=this.getProps();if(!rP(e,i,this.currentDirection))return;let{projection:r}=this.visualElement,n=this.getAxisMotionValue(e);if(r&&r.layout){let{min:i,max:s}=r.layout.layoutBox[e];n.set(t[e]-eC(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!p(e)||!i||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};ri(t=>{let e=this.getAxisMotionValue(t);if(e){let i=e.get();r[t]=function(t,e){let i=.5,r=iK(t),n=iK(e);return n>r?i=eX(e.min,e.max-r,t.min):r>n&&(i=eX(t.min,t.max-n,e.min)),U(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),ri(e=>{if(!rP(e,t,null))return;let i=this.getAxisMotionValue(e),{min:n,max:s}=this.constraints[e];i.set(eC(n,s,r[e]))})}addListeners(){if(!this.visualElement.current)return;rx.set(this.visualElement,this);let t=tU(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();p(t)&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,r=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),e();let n=tF(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(ri(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{n(),t(),r(),s&&s()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:r=!1,dragConstraints:n=!1,dragElastic:s=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:r,dragConstraints:n,dragElastic:s,dragMomentum:o}}}function rP(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class rw extends tY{constructor(t){super(t),this.removeGroupControls=tj,this.removeListeners=tj,this.controls=new rb(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||tj}unmount(){this.removeGroupControls(),this.removeListeners()}}let rS=t=>(e,i)=>{t&&tM.update(()=>t(e,i))};class rT extends tY{constructor(){super(...arguments),this.removePointerDownListener=tj}onPointerDown(t){this.session=new iG(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:ry(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:r}=this.node.getProps();return{onSessionStart:rS(t),onStart:rS(e),onMove:i,onEnd:(t,e)=>{delete this.session,r&&tM.update(()=>r(t,e))}}}mount(){this.removePointerDownListener=tU(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let rA={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function rj(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let rV={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!K.test(t))return t;t=parseFloat(t)}let i=rj(t,e.target.x),r=rj(t,e.target.y);return`${i}% ${r}%`}};class rC extends n.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:r}=this.props,{projection:n}=t;Object.assign(C,rE),n&&(e.group&&e.group.add(n),i&&i.register&&r&&i.register(n),n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),rA.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:r,isPresent:n}=this.props,s=i.projection;return s&&(s.isPresent=n,r||t.layoutDependency!==e||void 0===e?s.willUpdate():this.safeToRemove(),t.isPresent===n||(n?s.promote():s.relegate()||tM.postRender(()=>{let t=s.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),queueMicrotask(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:r}=t;r&&(r.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(r),i&&i.deregister&&i.deregister(r))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function rM(t){let[e,i]=function(){let t=(0,n.useContext)(a);if(null===t)return[!0,null];let{isPresent:e,onExitComplete:i,register:r}=t,s=(0,n.useId)();return(0,n.useEffect)(()=>r(s),[]),!e&&i?[!1,()=>i&&i(s)]:[!0]}(),r=(0,n.useContext)(S);return n.createElement(rC,{...t,layoutGroup:r,switchLayoutGroup:(0,n.useContext)(T),isPresent:e,safeToRemove:i})}let rE={borderRadius:{...rV,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:rV,borderTopRightRadius:rV,borderBottomLeftRadius:rV,borderBottomRightRadius:rV,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let r=ez.parse(t);if(r.length>5)return t;let n=ez.createTransformer(t),s="number"!=typeof r[0]?1:0,o=i.x.scale*e.x,a=i.y.scale*e.y;r[0+s]/=o,r[1+s]/=a;let l=eC(o,a,.5);return"number"==typeof r[2+s]&&(r[2+s]/=l),"number"==typeof r[3+s]&&(r[3+s]/=l),n(r)}}},rk=["TopLeft","TopRight","BottomLeft","BottomRight"],rD=rk.length,rR=t=>"string"==typeof t?parseFloat(t):t,rL=t=>"number"==typeof t||K.test(t);function rF(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let rN=rO(0,.5,ep),rB=rO(.5,.95,tj);function rO(t,e,i){return r=>r<t?0:r>e?1:i(eX(t,e,r))}function rU(t,e){t.min=e.min,t.max=e.max}function rI(t,e){rU(t.x,e.x),rU(t.y,e.y)}function r$(t,e,i,r,n){return t-=e,t=r+1/i*(t-r),void 0!==n&&(t=r+1/n*(t-r)),t}function rW(t,e,[i,r,n],s,o){!function(t,e=0,i=1,r=.5,n,s=t,o=t){if(Z.test(e)&&(e=parseFloat(e),e=eC(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=eC(s.min,s.max,r);t===s&&(a-=e),t.min=r$(t.min,e,i,a,n),t.max=r$(t.max,e,i,a,n)}(t,e[i],e[r],e[n],e.scale,s,o)}let rz=["x","scaleX","originX"],rH=["y","scaleY","originY"];function r_(t,e,i,r){rW(t.x,e,rz,i?i.x:void 0,r?r.x:void 0),rW(t.y,e,rH,i?i.y:void 0,r?r.y:void 0)}function rG(t){return 0===t.translate&&1===t.scale}function rY(t){return rG(t.x)&&rG(t.y)}function rq(t,e){return Math.round(t.x.min)===Math.round(e.x.min)&&Math.round(t.x.max)===Math.round(e.x.max)&&Math.round(t.y.min)===Math.round(e.y.min)&&Math.round(t.y.max)===Math.round(e.y.max)}function rX(t){return iK(t.x)/iK(t.y)}class rZ{constructor(){this.members=[]}add(t){iT(this.members,t),t.scheduleRender()}remove(t){if(iA(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e;let i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:r}=t.options;!1===r&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function rK(t,e,i){let r="",n=t.x.translate/e.x,s=t.y.translate/e.y;if((n||s)&&(r=`translate3d(${n}px, ${s}px, 0) `),(1!==e.x||1!==e.y)&&(r+=`scale(${1/e.x}, ${1/e.y}) `),i){let{rotate:t,rotateX:e,rotateY:n}=i;t&&(r+=`rotate(${t}deg) `),e&&(r+=`rotateX(${e}deg) `),n&&(r+=`rotateY(${n}deg) `)}let o=t.x.scale*e.x,a=t.y.scale*e.y;return(1!==o||1!==a)&&(r+=`scale(${o}, ${a})`),r||"none"}let rJ=(t,e)=>t.depth-e.depth;class rQ{constructor(){this.children=[],this.isDirty=!1}add(t){iT(this.children,t),this.isDirty=!0}remove(t){iA(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(rJ),this.isDirty=!1,this.children.forEach(t)}}let r0=["","X","Y","Z"],r1={visibility:"hidden"},r2=0,r3={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function r5({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:r,resetTransform:n}){return class{constructor(t={},i=null==e?void 0:e()){this.id=r2++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,r3.totalNodes=r3.resolvedTargetDeltas=r3.recalculatedProjection=0,this.nodes.forEach(r8),this.nodes.forEach(nn),this.nodes.forEach(ns),this.nodes.forEach(r9),window.MotionDebug&&window.MotionDebug.record(r3)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new rQ)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new ij),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e,i=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=e instanceof SVGElement&&"svg"!==e.tagName,this.instance=e;let{layoutId:r,layout:n,visualElement:s}=this.options;if(s&&!s.current&&s.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),i&&(n||r)&&(this.isLayoutDirty=!0),t){let i;let r=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=performance.now(),r=({timestamp:e})=>{let n=e-i;n>=250&&(tE(r),t(n-250))};return tM.read(r,!0),()=>tE(r)}(r,0),rA.hasAnimatedSinceResize&&(rA.hasAnimatedSinceResize=!1,this.nodes.forEach(nr))})}r&&this.root.registerSharedNode(r,this),!1!==this.options.animate&&s&&(r||n)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeTargetChanged:i,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let n=this.options.transition||s.getDefaultTransition()||nc,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=s.getProps(),l=!this.targetLayout||!rq(this.targetLayout,r)||i,u=!e&&i;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,u);let e={...ix(n,"layout"),onPlay:o,onComplete:a};(s.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||nr(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,tE(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(no),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(nt);return}this.isUpdating||this.nodes.forEach(ne),this.isUpdating=!1,this.nodes.forEach(ni),this.nodes.forEach(r4),this.nodes.forEach(r6),this.clearAllSnapshots();let t=performance.now();tk.delta=U(0,1e3/60,t-tk.timestamp),tk.timestamp=t,tk.isProcessing=!0,tD.update.process(tk),tD.preRender.process(tk),tD.render.process(tk),tk.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(r7),this.sharedNodes.forEach(na)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,tM.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){tM.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=re(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&(this.scroll={animationId:this.root.animationId,phase:t,isRoot:r(this.instance),offset:i(this.instance)})}resetTransform(){if(!n)return;let t=this.isLayoutDirty||this.shouldResetTransform,e=this.projectionDelta&&!rY(this.projectionDelta),i=this.getTransformTemplate(),r=i?i(this.latestValues,""):void 0,s=r!==this.prevTransformTemplateValue;t&&(e||ro(this.latestValues)||s)&&(n(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),r=this.removeElementScroll(i);return t&&(r=this.removeTransform(r)),nm((e=r).x),nm(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return re();let e=t.measureViewportBox(),{scroll:i}=this.root;return i&&(rd(e.x,i.offset.x),rd(e.y,i.offset.y)),e}removeElementScroll(t){let e=re();rI(e,t);for(let i=0;i<this.path.length;i++){let r=this.path[i],{scroll:n,options:s}=r;if(r!==this.root&&n&&s.layoutScroll){if(n.isRoot){rI(e,t);let{scroll:i}=this.root;i&&(rd(e.x,-i.offset.x),rd(e.y,-i.offset.y))}rd(e.x,n.offset.x),rd(e.y,n.offset.y)}}return e}applyTransform(t,e=!1){let i=re();rI(i,t);for(let t=0;t<this.path.length;t++){let r=this.path[t];!e&&r.options.layoutScroll&&r.scroll&&r!==r.root&&rg(i,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),ro(r.latestValues)&&rg(i,r.latestValues)}return ro(this.latestValues)&&rg(i,this.latestValues),i}removeTransform(t){let e=re();rI(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!ro(i.latestValues))continue;rs(i.latestValues)&&i.updateSnapshot();let r=re();rI(r,i.measurePageBox()),r_(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,r)}return ro(this.latestValues)&&r_(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==tk.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){var e,i,r,n;let s=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=s.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=s.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=s.isSharedProjectionDirty);let o=!!this.resumingFrom||this!==s;if(!(t||o&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty)||this.attemptToResolveRelativeTarget))return;let{layout:a,layoutId:l}=this.options;if(this.layout&&(a||l)){if(this.resolvedRelativeTargetAt=tk.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=re(),this.relativeTargetOrigin=re(),i3(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),rI(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=re(),this.targetWithTransforms=re()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),i=this.target,r=this.relativeTarget,n=this.relativeParent.target,i1(i.x,r.x,n.x),i1(i.y,r.y,n.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):rI(this.target,this.layout.layoutBox),rh(this.target,this.targetDelta)):rI(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=re(),this.relativeTargetOrigin=re(),i3(this.relativeTargetOrigin,this.target,t.target),rI(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}r3.resolvedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||rs(this.parent.latestValues)||ra(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;let e=this.getLead(),i=!!this.resumingFrom||this!==e,r=!0;if((this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty))&&(r=!1),i&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(r=!1),this.resolvedRelativeTargetAt===tk.timestamp&&(r=!1),r)return;let{layout:n,layoutId:s}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||s))return;rI(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,a=this.treeScale.y;(function(t,e,i,r=!1){let n,s;let o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){s=(n=i[a]).projectionDelta;let o=n.instance;(!o||!o.style||"contents"!==o.style.display)&&(r&&n.options.layoutScroll&&n.scroll&&n!==n.root&&rg(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),s&&(e.x*=s.x.scale,e.y*=s.y.scale,rh(t,s)),r&&ro(n.latestValues)&&rg(t,n.latestValues))}e.x=rc(e.x),e.y=rc(e.y)}})(this.layoutCorrected,this.treeScale,this.path,i),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox);let{target:l}=e;if(!l){this.projectionTransform&&(this.projectionDelta=i7(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=i7(),this.projectionDeltaWithTransform=i7());let u=this.projectionTransform;i0(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.projectionTransform=rK(this.projectionDelta,this.treeScale),(this.projectionTransform!==u||this.treeScale.x!==o||this.treeScale.y!==a)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),r3.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(t,e=!1){let i;let r=this.snapshot,n=r?r.latestValues:{},s={...this.latestValues},o=i7();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=re(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,c=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(nh));this.animationProgress=0,this.mixTargetDelta=e=>{let r=e/1e3;if(nl(o.x,t.x,r),nl(o.y,t.y,r),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,d,p,m;i3(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,nu(p.x,m.x,a.x,r),nu(p.y,m.y,a.y,r),i&&(u=this.relativeTarget,d=i,u.x.min===d.x.min&&u.x.max===d.x.max&&u.y.min===d.y.min&&u.y.max===d.y.max)&&(this.isProjectionDirty=!1),i||(i=re()),rI(i,this.relativeTarget)}l&&(this.animationValues=s,function(t,e,i,r,n,s){n?(t.opacity=eC(0,void 0!==i.opacity?i.opacity:1,rN(r)),t.opacityExit=eC(void 0!==e.opacity?e.opacity:1,0,rB(r))):s&&(t.opacity=eC(void 0!==e.opacity?e.opacity:1,void 0!==i.opacity?i.opacity:1,r));for(let n=0;n<rD;n++){let s=`border${rk[n]}Radius`,o=rF(e,s),a=rF(i,s);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||rL(o)===rL(a)?(t[s]=Math.max(eC(rR(o),rR(a),r),0),(Z.test(a)||Z.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||i.rotate)&&(t.rotate=eC(e.rotate||0,i.rotate||0,r))}(s,n,this.latestValues,r,c,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(tE(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=tM.update(()=>{rA.hasAnimatedSinceResize=!0,this.currentAnimation=function(t,e,i){let r=D(0)?0:iE(0);return r.start(iP("",r,1e3,i)),r.animation}(0,0,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:r,latestValues:n}=t;if(e&&i&&r){if(this!==t&&this.layout&&r&&nf(this.options.animationType,this.layout.layoutBox,r.layoutBox)){i=this.target||re();let e=iK(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let r=iK(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+r}rI(e,i),rg(e,n),i0(this.projectionDeltaWithTransform,this.layoutCorrected,e,n)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new rZ),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){var t;let{layoutId:e}=this.options;return e&&(null===(t=this.getStack())||void 0===t?void 0:t.lead)||this}getPrevLead(){var t;let{layoutId:e}=this.options;return e?null===(t=this.getStack())||void 0===t?void 0:t.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let r=this.getStack();r&&r.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.rotate||i.rotateX||i.rotateY||i.rotateZ)&&(e=!0),!e)return;let r={};for(let e=0;e<r0.length;e++){let n="rotate"+r0[e];i[n]&&(r[n]=i[n],t.setStaticValue(n,0))}for(let e in t.render(),r)t.setStaticValue(e,r[e]);t.scheduleRender()}getProjectionStyles(t){var e,i;if(!this.instance||this.isSVG)return;if(!this.isVisible)return r1;let r={visibility:""},n=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,r.opacity="",r.pointerEvents=tT(null==t?void 0:t.pointerEvents)||"",r.transform=n?n(this.latestValues,""):"none",r;let s=this.getLead();if(!this.projectionDelta||!this.layout||!s.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=tT(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!ro(this.latestValues)&&(e.transform=n?n({},""):"none",this.hasProjected=!1),e}let o=s.animationValues||s.latestValues;this.applyTransformsToTarget(),r.transform=rK(this.projectionDeltaWithTransform,this.treeScale,o),n&&(r.transform=n(o,r.transform));let{x:a,y:l}=this.projectionDelta;for(let t in r.transformOrigin=`${100*a.origin}% ${100*l.origin}% 0`,s.animationValues?r.opacity=s===this?null!==(i=null!==(e=o.opacity)&&void 0!==e?e:this.latestValues.opacity)&&void 0!==i?i:1:this.preserveOpacity?this.latestValues.opacity:o.opacityExit:r.opacity=s===this?void 0!==o.opacity?o.opacity:"":void 0!==o.opacityExit?o.opacityExit:0,C){if(void 0===o[t])continue;let{correct:e,applyTo:i}=C[t],n="none"===r.transform?o[t]:e(o[t],s);if(i){let t=i.length;for(let e=0;e<t;e++)r[i[e]]=n}else r[t]=n}return this.options.layoutId&&(r.pointerEvents=s===this?tT(null==t?void 0:t.pointerEvents)||"":"none"),r}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>{var e;return null===(e=t.currentAnimation)||void 0===e?void 0:e.stop()}),this.root.nodes.forEach(nt),this.root.sharedNodes.clear()}}}function r4(t){t.updateLayout()}function r6(t){var e;let i=(null===(e=t.resumeFrom)||void 0===e?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&i&&t.hasListeners("didUpdate")){let{layoutBox:e,measuredBox:r}=t.layout,{animationType:n}=t.options,s=i.source!==t.layout.source;"size"===n?ri(t=>{let r=s?i.measuredBox[t]:i.layoutBox[t],n=iK(r);r.min=e[t].min,r.max=r.min+n}):nf(n,i.layoutBox,e)&&ri(r=>{let n=s?i.measuredBox[r]:i.layoutBox[r],o=iK(e[r]);n.max=n.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[r].max=t.relativeTarget[r].min+o)});let o=i7();i0(o,e,i.layoutBox);let a=i7();s?i0(a,t.applyTransform(r,!0),i.measuredBox):i0(a,e,i.layoutBox);let l=!rY(o),u=!1;if(!t.resumeFrom){let r=t.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:n,layout:s}=r;if(n&&s){let o=re();i3(o,i.layoutBox,n.layoutBox);let a=re();i3(a,e,s.layoutBox),rq(o,a)||(u=!0),r.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=r)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:i,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function r8(t){r3.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function r9(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function r7(t){t.clearSnapshot()}function nt(t){t.clearMeasurements()}function ne(t){t.isLayoutDirty=!1}function ni(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function nr(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function nn(t){t.resolveTargetDelta()}function ns(t){t.calcProjection()}function no(t){t.resetRotation()}function na(t){t.removeLeadSnapshot()}function nl(t,e,i){t.translate=eC(e.translate,0,i),t.scale=eC(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function nu(t,e,i,r){t.min=eC(e.min,i.min,r),t.max=eC(e.max,i.max,r)}function nh(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let nc={duration:.45,ease:[.4,0,.1,1]},nd=t=>"undefined"!=typeof navigator&&navigator.userAgent.toLowerCase().includes(t),np=nd("applewebkit/")&&!nd("chrome/")?Math.round:tj;function nm(t){t.min=np(t.min),t.max=np(t.max)}function nf(t,e,i){return"position"===t||"preserve-aspect"===t&&!iJ(rX(e),rX(i),.2)}let ng=r5({attachResizeListener:(t,e)=>tF(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),nv={current:void 0},ny=r5({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!nv.current){let t=new ng({});t.mount(window),t.setOptions({layoutScroll:!0}),nv.current=t}return nv.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position}),nx=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function nb(t,e,i=1){tj(i<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[r,n]=function(t){let e=nx.exec(t);if(!e)return[,];let[,i,r]=e;return[i,r]}(t);if(!r)return;let s=window.getComputedStyle(e).getPropertyValue(r);if(s){let t=s.trim();return iS(t)?parseFloat(t):t}return B(n)?nb(n,e,i+1):n}let nP=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),nw=t=>nP.has(t),nS=t=>Object.keys(t).some(nw),nT=t=>t===I||t===K,nA=(t,e)=>parseFloat(t.split(", ")[e]),nj=(t,e)=>(i,{transform:r})=>{if("none"===r||!r)return 0;let n=r.match(/^matrix3d\((.+)\)$/);if(n)return nA(n[1],e);{let e=r.match(/^matrix\((.+)\)$/);return e?nA(e[1],t):0}},nV=new Set(["x","y","z"]),nC=M.filter(t=>!nV.has(t)),nM={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:nj(4,13),y:nj(5,14)};nM.translateX=nM.x,nM.translateY=nM.y;let nE=(t,e,i)=>{let r=e.measureViewportBox(),n=getComputedStyle(e.current),{display:s}=n,o={};"none"===s&&e.setStaticValue("display",t.display||"block"),i.forEach(t=>{o[t]=nM[t](r,n)}),e.render();let a=e.measureViewportBox();return i.forEach(i=>{let r=e.getValue(i);r&&r.jump(o[i]),t[i]=nM[i](a,n)}),t},nk=(t,e,i={},r={})=>{e={...e},r={...r};let n=Object.keys(e).filter(nw),s=[],o=!1,a=[];if(n.forEach(n=>{let l;let u=t.getValue(n);if(!t.hasValue(n))return;let h=i[n],c=iR(h),d=e[n];if(tP(d)){let t=d.length,e=null===d[0]?1:0;c=iR(h=d[e]);for(let i=e;i<t&&null!==d[i];i++)l?tj(iR(d[i])===l,"All keyframes must be of the same type"):tj((l=iR(d[i]))===c||nT(c)&&nT(l),"Keyframes must be of the same dimension as the current value")}else l=iR(d);if(c!==l){if(nT(c)&&nT(l)){let t=u.get();"string"==typeof t&&u.set(parseFloat(t)),"string"==typeof d?e[n]=parseFloat(d):Array.isArray(d)&&l===K&&(e[n]=d.map(parseFloat))}else(null==c?void 0:c.transform)&&(null==l?void 0:l.transform)&&(0===h||0===d)?0===h?u.set(l.transform(h)):e[n]=c.transform(d):(o||(s=function(t){let e=[];return nC.forEach(i=>{let r=t.getValue(i);void 0!==r&&(e.push([i,r.get()]),r.set(i.startsWith("scale")?1:0))}),e.length&&t.render(),e}(t),o=!0),a.push(n),r[n]=void 0!==r[n]?r[n]:e[n],u.jump(d))}}),!a.length)return{target:e,transitionEnd:r};{let i=a.indexOf("height")>=0?window.pageYOffset:null,n=nE(e,t,a);return s.length&&s.forEach(([e,i])=>{t.getValue(e).set(i)}),t.render(),l&&null!==i&&window.scrollTo({top:i}),{target:n,transitionEnd:r}}},nD=(t,e,i,r)=>{let n=function(t,{...e},i){let r=t.current;if(!(r instanceof Element))return{target:e,transitionEnd:i};for(let n in i&&(i={...i}),t.values.forEach(t=>{let e=t.get();if(!B(e))return;let i=nb(e,r);i&&t.set(i)}),e){let t=e[n];if(!B(t))continue;let s=nb(t,r);s&&(e[n]=s,i||(i={}),void 0===i[n]&&(i[n]=t))}return{target:e,transitionEnd:i}}(t,e,r);return function(t,e,i,r){return nS(e)?nk(t,e,i,r):{target:e,transitionEnd:r}}(t,e=n.target,i,r=n.transitionEnd)},nR={current:null},nL={current:!1},nF=new WeakMap,nN=Object.keys(w),nB=nN.length,nO=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],nU=v.length;class nI{constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:r,visualState:n},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>tM.render(this.render,!1,!0);let{latestValues:o,renderState:a}=n;this.latestValues=o,this.baseTarget={...o},this.initialValues=e.initial?{...o}:{},this.renderState=a,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=r,this.options=s,this.isControllingVariants=y(e),this.isVariantNode=x(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:l,...u}=this.scrapeMotionValuesFromProps(e,{});for(let t in u){let e=u[t];void 0!==o[t]&&D(e)&&(e.set(o[t],!1),iw(l)&&l.add(t))}}scrapeMotionValuesFromProps(t,e){return{}}mount(t){this.current=t,nF.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),nL.current||function(){if(nL.current=!0,l){if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>nR.current=t.matches;t.addListener(e),e()}else nR.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||nR.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in nF.delete(this.current),this.projection&&this.projection.unmount(),tE(this.notifyUpdate),tE(this.render),this.valueSubscriptions.forEach(t=>t()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features)this.features[t].unmount();this.current=null}bindToMotionValue(t,e){let i=E.has(t),r=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&tM.update(this.notifyUpdate,!1,!0),i&&this.projection&&(this.projection.isTransformDirty=!0)}),n=e.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(t,()=>{r(),n()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}loadFeatures({children:t,...e},i,r,n){let s,o;for(let t=0;t<nB;t++){let i=nN[t],{isEnabled:r,Feature:n,ProjectionNode:a,MeasureLayout:l}=w[i];a&&(s=a),r(e)&&(!this.features[i]&&n&&(this.features[i]=new n(this)),l&&(o=l))}if(("html"===this.type||"svg"===this.type)&&!this.projection&&s){this.projection=new s(this.latestValues,this.parent&&this.parent.projection);let{layoutId:t,layout:i,drag:r,dragConstraints:o,layoutScroll:a,layoutRoot:l}=e;this.projection.setOptions({layoutId:t,layout:i,alwaysMeasureLayout:!!r||o&&p(o),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:"string"==typeof i?i:"both",initialPromotionConfig:n,layoutScroll:a,layoutRoot:l})}return o}updateFeatures(){for(let t in this.features){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):re()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}makeTargetAnimatable(t,e=!0){return this.makeTargetAnimatableFromInstance(t,this.props,e)}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<nO.length;e++){let i=nO[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let r=t["on"+i];r&&(this.propEventSubscriptions[i]=this.on(i,r))}this.prevMotionValues=function(t,e,i){let{willChange:r}=e;for(let n in e){let s=e[n],o=i[n];if(D(s))t.addValue(n,s),iw(r)&&r.add(n);else if(D(o))t.addValue(n,iE(s,{owner:t})),iw(r)&&r.remove(n);else if(o!==s){if(t.hasValue(n)){let e=t.getValue(n);e.hasAnimated||e.set(s)}else{let e=t.getStaticValue(n);t.addValue(n,iE(void 0!==e?e:s,{owner:t}))}}}for(let r in i)void 0===e[r]&&t.removeValue(r);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(t=!1){if(t)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){let t=this.parent&&this.parent.getVariantContext()||{};return void 0!==this.props.initial&&(t.initial=this.props.initial),t}let e={};for(let t=0;t<nU;t++){let i=v[t],r=this.props[i];(m(r)||!1===r)&&(e[i]=r)}return e}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){e!==this.values.get(t)&&(this.removeValue(t),this.bindToMotionValue(t,e)),this.values.set(t,e),this.latestValues[t]=e.get()}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=iE(e,{owner:this}),this.addValue(t,i)),i}readValue(t){var e;return void 0===this.latestValues[t]&&this.current?null!==(e=this.getBaseTargetFromProps(this.props,t))&&void 0!==e?e:this.readValueFromInstance(this.current,t,this.options):this.latestValues[t]}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){var e;let{initial:i}=this.props,r="string"==typeof i||"object"==typeof i?null===(e=tb(this.props,i))||void 0===e?void 0:e[t]:void 0;if(i&&void 0!==r)return r;let n=this.getBaseTargetFromProps(this.props,t);return void 0===n||D(n)?void 0!==this.initialValues[t]&&void 0===r?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new ij),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class n$ extends nI{sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}makeTargetAnimatableFromInstance({transition:t,transitionEnd:e,...i},{transformValues:r},n){let s=function(t,e,i){let r={};for(let n in t){let t=function(t,e){if(e)return(e[t]||e.default||e).from}(n,e);if(void 0!==t)r[n]=t;else{let t=i.getValue(n);t&&(r[n]=t.get())}}return r}(i,t||{},this);if(r&&(e&&(e=r(e)),i&&(i=r(i)),s&&(s=r(s))),n){!function(t,e,i){var r,n;let s=Object.keys(e).filter(e=>!t.hasValue(e)),o=s.length;if(o)for(let a=0;a<o;a++){let o=s[a],l=e[o],u=null;Array.isArray(l)&&(u=l[0]),null===u&&(u=null!==(n=null!==(r=i[o])&&void 0!==r?r:t.readValue(o))&&void 0!==n?n:e[o]),null!=u&&("string"==typeof u&&(iS(u)||iy(u))?u=parseFloat(u):!iF(u)&&ez.test(l)&&(u=iv(o,l)),t.addValue(o,iE(u,{owner:t})),void 0===i[o]&&(i[o]=u),null!==u&&t.setBaseTarget(o,u))}}(this,i,s);let t=nD(this,i,s,e);e=t.transitionEnd,i=t.target}return{transition:t,transitionEnd:e,...i}}}class nW extends n${constructor(){super(...arguments),this.type="html"}readValueFromInstance(t,e){if(E.has(e)){let t=ig(e);return t&&t.default||0}{let i=window.getComputedStyle(t),r=(N(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(t,{transformPagePoint:e}){return rv(t,e)}build(t,e,i,r){tr(t,e,i,r.transformTemplate)}scrapeMotionValuesFromProps(t,e){return ty(t,e)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;D(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}renderInstance(t,e,i,r){tf(t,e,i,r)}}class nz extends n${constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(E.has(e)){let t=ig(e);return t&&t.default||0}return e=tg.has(e)?e:c(e),t.getAttribute(e)}measureInstanceViewportBox(){return re()}scrapeMotionValuesFromProps(t,e){return tx(t,e)}build(t,e,i,r){td(t,e,i,this.isSVGTag,r.transformTemplate)}renderInstance(t,e,i,r){tv(t,e,i,r)}mount(t){this.isSVGTag=tm(t.tagName),super.mount(t)}}let nH=(t,e)=>V(t)?new nz(e,{enableHardwareAcceleration:!1}):new nW(e,{enableHardwareAcceleration:!0}),n_={animation:{Feature:iW},exit:{Feature:iH},inView:{Feature:t4},tap:{Feature:tQ},focus:{Feature:tZ},hover:{Feature:tX},pan:{Feature:rT},drag:{Feature:rw,ProjectionNode:ny,MeasureLayout:rM},layout:{ProjectionNode:ny,MeasureLayout:rM}},nG=function(t){function e(i,r={}){return function({preloadedFeatures:t,createVisualElement:e,useRender:i,useVisualState:r,Component:c}){t&&function(t){for(let e in t)w[e]={...w[e],...t[e]}}(t);let f=(0,n.forwardRef)(function(f,g){var v;let x;let P={...(0,n.useContext)(s),...f,layoutId:function({layoutId:t}){let e=(0,n.useContext)(S).id;return e&&void 0!==t?e+"-"+t:t}(f)},{isStatic:w}=P,A=function(t){let{initial:e,animate:i}=function(t,e){if(y(t)){let{initial:e,animate:i}=t;return{initial:!1===e||m(e)?e:void 0,animate:m(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,n.useContext)(o));return(0,n.useMemo)(()=>({initial:e,animate:i}),[b(e),b(i)])}(f),j=r(f,w);if(!w&&l){A.visualElement=function(t,e,i,r){let{visualElement:l}=(0,n.useContext)(o),c=(0,n.useContext)(h),p=(0,n.useContext)(a),m=(0,n.useContext)(s).reducedMotion,f=(0,n.useRef)();r=r||c.renderer,!f.current&&r&&(f.current=r(t,{visualState:e,parent:l,props:i,presenceContext:p,blockInitialAnimation:!!p&&!1===p.initial,reducedMotionConfig:m}));let g=f.current;(0,n.useInsertionEffect)(()=>{g&&g.update(i,p)});let v=(0,n.useRef)(!!(i[d]&&!window.HandoffComplete));return u(()=>{g&&(g.render(),v.current&&g.animationState&&g.animationState.animateChanges())}),(0,n.useEffect)(()=>{g&&(g.updateFeatures(),!v.current&&g.animationState&&g.animationState.animateChanges(),v.current&&(v.current=!1,window.HandoffComplete=!0))}),g}(c,j,P,e);let i=(0,n.useContext)(T),r=(0,n.useContext)(h).strict;A.visualElement&&(x=A.visualElement.loadFeatures(P,r,t,i))}return n.createElement(o.Provider,{value:A},x&&A.visualElement?n.createElement(x,{visualElement:A.visualElement,...P}):null,i(c,f,(v=A.visualElement,(0,n.useCallback)(t=>{t&&j.mount&&j.mount(t),v&&(t?v.mount(t):v.unmount()),g&&("function"==typeof g?g(t):p(g)&&(g.current=t))},[v])),j,w,A.visualElement))});return f[A]=c,f}(t(i,r))}if("undefined"==typeof Proxy)return e;let i=new Map;return new Proxy(e,{get:(t,r)=>(i.has(r)||i.set(r,e(r)),i.get(r))})}((t,e)=>(function(t,{forwardMotionProps:e=!1},i,r){return{...V(t)?tR:tL,preloadedFeatures:i,useRender:function(t=!1){return(e,i,r,{latestValues:s},o)=>{let a=(V(e)?function(t,e,i,r){let s=(0,n.useMemo)(()=>{let i=tp();return td(i,e,{enableHardwareAcceleration:!1},tm(r),t.transformTemplate),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};ts(e,t.style,t),s.style={...e,...s.style}}return s}:function(t,e,i){let r={},s=function(t,e,i){let r=t.style||{},s={};return ts(s,r,t),Object.assign(s,function({transformTemplate:t},e,i){return(0,n.useMemo)(()=>{let r=tn();return tr(r,e,{enableHardwareAcceleration:!i},t),Object.assign({},r.vars,r.style)},[e])}(t,e,i)),t.transformValues?t.transformValues(s):s}(t,e,i);return t.drag&&!1!==t.dragListener&&(r.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(r.tabIndex=0),r.style=s,r})(i,s,o,e),l={...function(t,e,i){let r={};for(let n in t)("values"!==n||"object"!=typeof t.values)&&(tl(n)||!0===i&&ta(n)||!e&&!ta(n)||t.draggable&&n.startsWith("onDrag"))&&(r[n]=t[n]);return r}(i,"string"==typeof e,t),...a,ref:r},{children:u}=i,h=(0,n.useMemo)(()=>D(u)?u.get():u,[u]);return(0,n.createElement)(e,{...l,children:h})}}(e),createVisualElement:r,Component:t}})(t,e,n_,nH));function nY({onGenerate:t,isGenerating:e}){let[i,s]=(0,n.useState)("");return(0,r.jsxs)(nG.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"glass-effect p-8 rounded-lg max-w-md w-full",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-center mb-6",children:"Generate Your Flow"}),(0,r.jsxs)("form",{onSubmit:r=>{r.preventDefault(),i.trim()&&!e&&t(i.trim())},className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"prompt",className:"block text-sm font-medium text-gray-300 mb-2",children:"Describe your ideal coding atmosphere"}),(0,r.jsx)("textarea",{id:"prompt",value:i,onChange:t=>s(t.target.value),placeholder:"e.g., Peaceful rain sounds with soft piano for deep focus...",className:"w-full p-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 resize-none",rows:3,disabled:e})]}),(0,r.jsx)("button",{type:"submit",disabled:!i.trim()||e,className:"w-full bg-flow-accent hover:bg-flow-accent/80 disabled:bg-gray-600 disabled:cursor-not-allowed text-white py-3 px-6 rounded-lg font-medium transition-colors",children:e?"Generating Flow...":"Generate Flow"})]}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsx)("p",{className:"text-sm text-gray-400 mb-3",children:"Or try a preset:"}),(0,r.jsx)("div",{className:"space-y-2",children:["Rainy day coding session","Cyberpunk neon vibes","Forest morning ambience","Retro 8-bit soundscape","Deep focus meditation"].map((t,i)=>(0,r.jsx)("button",{onClick:()=>s(t),disabled:e,className:"w-full text-left p-2 text-sm bg-white/5 hover:bg-white/10 disabled:hover:bg-white/5 rounded border border-white/10 transition-colors",children:t},i))})]})]})}var nq=i(3353);let nX=i.n(nq)()(async()=>{},{loadableGenerated:{modules:["app/components/DynamicBackground.tsx -> ./ThreeJSBackground"]},ssr:!1,loading:()=>(0,r.jsx)("div",{className:"absolute inset-0 gradient-bg"})});function nZ({prompt:t}){let[e,i]=(0,n.useState)(!1);return e?(0,r.jsx)("div",{className:"absolute inset-0 gradient-bg",children:(0,r.jsx)(nX,{prompt:t})}):(0,r.jsx)("div",{className:"absolute inset-0 gradient-bg"})}function nK({audioUrl:t,prompt:e,onNewFlow:i,isFlowMode:s,onToggleFlowMode:o}){let[a,l]=(0,n.useState)(!1),[u,h]=(0,n.useState)(.7),[c,d]=(0,n.useState)(0),[p,m]=(0,n.useState)(0),f=(0,n.useRef)(null),g=t=>{let e=Math.floor(t/60),i=Math.floor(t%60);return`${e}:${i.toString().padStart(2,"0")}`};return(0,r.jsxs)(nG.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:`glass-effect p-6 rounded-lg max-w-md w-full ${s?"fixed bottom-4 right-4 max-w-xs":""}`,children:[(0,r.jsx)("audio",{ref:f,src:t}),!s&&(0,r.jsxs)("div",{className:"text-center mb-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Now Playing"}),(0,r.jsx)("p",{className:"text-sm text-gray-300 truncate",children:e})]}),(0,r.jsxs)("div",{className:"flex items-center gap-4 mb-4",children:[(0,r.jsx)("button",{onClick:()=>{let t=f.current;t&&(a?t.pause():t.play(),l(!a))},className:"bg-flow-accent hover:bg-flow-accent/80 text-white p-3 rounded-full transition-colors",children:a?(0,r.jsx)("svg",{className:"w-6 h-6",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z",clipRule:"evenodd"})}):(0,r.jsx)("svg",{className:"w-6 h-6",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z",clipRule:"evenodd"})})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-300",children:[(0,r.jsx)("span",{children:g(c)}),(0,r.jsx)("div",{className:"flex-1 bg-gray-600 rounded-full h-1",children:(0,r.jsx)("div",{className:"bg-flow-accent h-1 rounded-full transition-all",style:{width:`${p?c/p*100:0}%`}})}),(0,r.jsx)("span",{children:g(p)})]})})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,r.jsx)("svg",{className:"w-4 h-4 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.824L4.5 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.5l3.883-3.824z",clipRule:"evenodd"})}),(0,r.jsx)("input",{type:"range",min:"0",max:"1",step:"0.1",value:u,onChange:t=>{let e=parseFloat(t.target.value);h(e),f.current&&(f.current.volume=e)},className:"flex-1"})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)("button",{onClick:i,className:"flex-1 bg-gray-600 hover:bg-gray-500 text-white py-2 px-4 rounded transition-colors",children:"New Flow"}),(0,r.jsx)("button",{onClick:o,className:"bg-flow-accent hover:bg-flow-accent/80 text-white py-2 px-4 rounded transition-colors",children:s?"Exit Flow":"Flow Mode"})]})]})}function nJ({onSessionComplete:t}){let[e,i]=(0,n.useState)(1500),[s,o]=(0,n.useState)(!1),[a,l]=(0,n.useState)(!1),[u,h]=(0,n.useState)(0);return(0,r.jsx)(nG.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},className:"glass-effect p-4 rounded-lg",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-gray-300 mb-2",children:a?"Break Time":"Focus Session"}),(0,r.jsx)("div",{className:"text-2xl font-mono font-bold mb-4",children:(t=>{let e=Math.floor(t/60);return`${e.toString().padStart(2,"0")}:${(t%60).toString().padStart(2,"0")}`})(e)}),(0,r.jsxs)("div",{className:"flex gap-2 justify-center mb-3",children:[(0,r.jsx)("button",{onClick:()=>o(!s),className:"bg-flow-accent hover:bg-flow-accent/80 text-white px-4 py-2 rounded text-sm transition-colors",children:s?"Pause":"Start"}),(0,r.jsx)("button",{onClick:()=>{o(!1),i(1500),l(!1)},className:"bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded text-sm transition-colors",children:"Reset"})]}),(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["Sessions completed: ",u]})]})})}function nQ(){let[t,e]=(0,n.useState)([]),[i,s]=(0,n.useState)(""),o=i=>{e(t.map(t=>t.id===i?{...t,completed:!t.completed}:t))},a=i=>{e(t.filter(t=>t.id!==i))};return(0,r.jsxs)(nG.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},className:"glass-effect p-4 rounded-lg w-80",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-gray-300 mb-3",children:"Session Tasks"}),(0,r.jsx)("form",{onSubmit:r=>{r.preventDefault(),i.trim()&&(e([...t,{id:Date.now(),text:i.trim(),completed:!1}]),s(""))},className:"mb-3",children:(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)("input",{type:"text",value:i,onChange:t=>s(t.target.value),placeholder:"Add a task...",className:"flex-1 p-2 text-sm bg-white/10 border border-white/20 rounded text-white placeholder-gray-400"}),(0,r.jsx)("button",{type:"submit",className:"bg-flow-accent hover:bg-flow-accent/80 text-white px-3 py-2 rounded text-sm transition-colors",children:"Add"})]})}),(0,r.jsx)("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:t.map(t=>(0,r.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-white/5 rounded",children:[(0,r.jsx)("input",{type:"checkbox",checked:t.completed,onChange:()=>o(t.id),className:"rounded"}),(0,r.jsx)("span",{className:`flex-1 text-sm ${t.completed?"line-through text-gray-400":"text-white"}`,children:t.text}),(0,r.jsx)("button",{onClick:()=>a(t.id),className:"text-red-400 hover:text-red-300 text-sm",children:"\xd7"})]},t.id))}),0===t.length&&(0,r.jsx)("p",{className:"text-gray-400 text-sm text-center py-4",children:"No tasks yet. Add one above!"})]})}function n0(){return(0,r.jsxs)(nG.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"fixed bottom-6 left-6 right-6 flex gap-4 justify-center",children:[(0,r.jsx)(nJ,{}),(0,r.jsx)(nQ,{})]})}function n1(){let[t,e]=(0,n.useState)(""),[i,s]=(0,n.useState)(!1),[o,a]=(0,n.useState)(null),[l,u]=(0,n.useState)(!1),h=async t=>{e(t),s(!0);try{let e=await fetch("/api/generate-audio",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({prompt:t})});if(!e.ok)throw Error("Failed to generate audio");let i=await e.json();a(i.audioUrl)}catch(t){console.error("Failed to generate flow:",t)}finally{s(!1)}};return(0,r.jsxs)("main",{className:"relative h-screen w-full overflow-hidden",children:[(0,r.jsx)(nZ,{prompt:t}),(0,r.jsxs)("div",{className:"relative z-10 h-full flex flex-col",children:[!l&&(0,r.jsx)("header",{className:"p-6",children:(0,r.jsxs)("h1",{className:"text-3xl font-bold text-center mb-8",children:["Code",(0,r.jsx)("span",{className:"text-flow-accent",children:"Flow"})]})}),(0,r.jsx)("div",{className:"flex-1 flex items-center justify-center px-6",children:o?(0,r.jsx)(nK,{audioUrl:o,prompt:t,onNewFlow:()=>a(null),isFlowMode:l,onToggleFlowMode:()=>u(!l)}):(0,r.jsx)(nY,{onGenerate:h,isGenerating:i})}),!l&&o&&(0,r.jsx)(n0,{})]})]})}},3353:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"default",{enumerable:!0,get:function(){return n}});let r=i(1174)._(i(7028));function n(t,e){var i;let n={};"function"==typeof t&&(n.loader=t);let s={...n,...e};return(0,r.default)({...s,modules:null==(i=s.loadableGenerated)?void 0:i.modules})}("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},4114:(t,e)=>{"use strict";function i(t){return t.split("/").map(t=>encodeURIComponent(t)).join("/")}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"encodeURIPath",{enumerable:!0,get:function(){return i}})},933:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"BailoutToCSR",{enumerable:!0,get:function(){return n}});let r=i(4129);function n(t){let{reason:e,children:i}=t;throw new r.BailoutToCSRError(e)}},7028:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"default",{enumerable:!0,get:function(){return u}});let r=i(8819),n=i(7266),s=i(933),o=i(3392);function a(t){return{default:t&&"default"in t?t.default:t}}let l={loader:()=>Promise.resolve(a(()=>null)),loading:null,ssr:!0},u=function(t){let e={...l,...t},i=(0,n.lazy)(()=>e.loader().then(a)),u=e.loading;function h(t){let a=u?(0,r.jsx)(u,{isLoading:!0,pastDelay:!0,error:null}):null,l=!e.ssr||!!e.loading,h=l?n.Suspense:n.Fragment,c=e.ssr?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(o.PreloadChunks,{moduleIds:e.modules}),(0,r.jsx)(i,{...t})]}):(0,r.jsx)(s.BailoutToCSR,{reason:"next/dynamic",children:(0,r.jsx)(i,{...t})});return(0,r.jsx)(h,{...l?{fallback:a}:{},children:c})}return h.displayName="LoadableComponent",h}},3392:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"PreloadChunks",{enumerable:!0,get:function(){return a}});let r=i(8819),n=i(5305),s=i(9348),o=i(4114);function a(t){let{moduleIds:e}=t,i=s.workAsyncStorage.getStore();if(void 0===i)return null;let a=[];if(i.reactLoadableManifest&&e){let t=i.reactLoadableManifest;for(let i of e){if(!t[i])continue;let e=t[i].files;a.push(...e)}}return 0===a.length?null:(0,r.jsx)(r.Fragment,{children:a.map(t=>{let e=i.assetPrefix+"/_next/"+(0,o.encodeURIPath)(t);return t.endsWith(".css")?(0,r.jsx)("link",{precedence:"dynamic",href:e,rel:"stylesheet",as:"style"},t):((0,n.preload)(e,{as:"script",fetchPriority:"low"}),null)})})}},6877:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>a,metadata:()=>o});var r=i(9351),n=i(5384),s=i.n(n);i(7272);let o={title:"CodeFlow - AI-Powered Ambient Coding Environment",description:"Get into flow state with AI-generated soundscapes and dynamic visuals designed for programmers."};function a({children:t}){return(0,r.jsx)("html",{lang:"en",className:"dark",children:(0,r.jsx)("body",{className:`${s().className} bg-flow-dark text-white antialiased`,children:t})})}},9012:(t,e,i)=>{"use strict";i.a(t,async(t,r)=>{try{i.r(e),i.d(e,{default:()=>t});var n=i(1851);let t=(await (0,n.createProxy)(String.raw`/Users/<USER>/Projects/flow/app/page.tsx`)).default;r()}catch(t){r(t)}},1)},7272:()=>{}};var e=require("../webpack-runtime.js");e.C(t);var i=t=>e(e.s=t),r=e.X(0,[267,581],()=>i(8823));module.exports=r})();