/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fcoryjanowski%2FProjects%2Fflow&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fcoryjanowski%2FProjects%2Fflow&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?2c4b\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst page2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page2, \"/Users/<USER>/Projects/flow/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Projects/flow/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Projects/flow/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fcoryjanowski%2FProjects%2Fflow&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGY29yeWphbm93c2tpJTJGUHJvamVjdHMlMkZmbG93JTJGYXBwJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdJQUFtRiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvZGVmbG93Lz9hOGVjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2NvcnlqYW5vd3NraS9Qcm9qZWN0cy9mbG93L2FwcC9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/components/AudioPlayer.tsx":
/*!****************************************!*\
  !*** ./app/components/AudioPlayer.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AudioPlayer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction AudioPlayer({ audioUrl, prompt, onNewFlow, isFlowMode, onToggleFlowMode }) {\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [volume, setVolume] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0.7);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [duration, setDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const audio = audioRef.current;\n        if (!audio) return;\n        const updateTime = ()=>setCurrentTime(audio.currentTime);\n        const updateDuration = ()=>setDuration(audio.duration);\n        audio.addEventListener('timeupdate', updateTime);\n        audio.addEventListener('loadedmetadata', updateDuration);\n        audio.addEventListener('ended', ()=>setIsPlaying(false));\n        return ()=>{\n            audio.removeEventListener('timeupdate', updateTime);\n            audio.removeEventListener('loadedmetadata', updateDuration);\n            audio.removeEventListener('ended', ()=>setIsPlaying(false));\n        };\n    }, [\n        audioUrl\n    ]);\n    const togglePlay = ()=>{\n        const audio = audioRef.current;\n        if (!audio) return;\n        if (isPlaying) {\n            audio.pause();\n        } else {\n            audio.play();\n        }\n        setIsPlaying(!isPlaying);\n    };\n    const handleVolumeChange = (e)=>{\n        const newVolume = parseFloat(e.target.value);\n        setVolume(newVolume);\n        if (audioRef.current) {\n            audioRef.current.volume = newVolume;\n        }\n    };\n    const formatTime = (time)=>{\n        const minutes = Math.floor(time / 60);\n        const seconds = Math.floor(time % 60);\n        return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        className: `glass-effect p-6 rounded-lg max-w-md w-full ${isFlowMode ? 'fixed bottom-4 right-4 max-w-xs' : ''}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                ref: audioRef,\n                src: audioUrl\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/flow/app/components/AudioPlayer.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this),\n            !isFlowMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold mb-2\",\n                        children: \"Now Playing\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/flow/app/components/AudioPlayer.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-300 truncate\",\n                        children: prompt\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/flow/app/components/AudioPlayer.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/flow/app/components/AudioPlayer.tsx\",\n                lineNumber: 80,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-4 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: togglePlay,\n                        className: \"bg-flow-accent hover:bg-flow-accent/80 text-white p-3 rounded-full transition-colors\",\n                        children: isPlaying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-6 h-6\",\n                            fill: \"currentColor\",\n                            viewBox: \"0 0 20 20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                fillRule: \"evenodd\",\n                                d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z\",\n                                clipRule: \"evenodd\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/flow/app/components/AudioPlayer.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/flow/app/components/AudioPlayer.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-6 h-6\",\n                            fill: \"currentColor\",\n                            viewBox: \"0 0 20 20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                fillRule: \"evenodd\",\n                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\",\n                                clipRule: \"evenodd\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/flow/app/components/AudioPlayer.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/flow/app/components/AudioPlayer.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/flow/app/components/AudioPlayer.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 text-sm text-gray-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: formatTime(currentTime)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/flow/app/components/AudioPlayer.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 bg-gray-600 rounded-full h-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-flow-accent h-1 rounded-full transition-all\",\n                                        style: {\n                                            width: `${duration ? currentTime / duration * 100 : 0}%`\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Projects/flow/app/components/AudioPlayer.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/flow/app/components/AudioPlayer.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: formatTime(duration)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/flow/app/components/AudioPlayer.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/flow/app/components/AudioPlayer.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/flow/app/components/AudioPlayer.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/flow/app/components/AudioPlayer.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-gray-400\",\n                        fill: \"currentColor\",\n                        viewBox: \"0 0 20 20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            fillRule: \"evenodd\",\n                            d: \"M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.824L4.5 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.5l3.883-3.824z\",\n                            clipRule: \"evenodd\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/flow/app/components/AudioPlayer.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/flow/app/components/AudioPlayer.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"range\",\n                        min: \"0\",\n                        max: \"1\",\n                        step: \"0.1\",\n                        value: volume,\n                        onChange: handleVolumeChange,\n                        className: \"flex-1\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/flow/app/components/AudioPlayer.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/flow/app/components/AudioPlayer.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onNewFlow,\n                        className: \"flex-1 bg-gray-600 hover:bg-gray-500 text-white py-2 px-4 rounded transition-colors\",\n                        children: \"New Flow\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/flow/app/components/AudioPlayer.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onToggleFlowMode,\n                        className: \"bg-flow-accent hover:bg-flow-accent/80 text-white py-2 px-4 rounded transition-colors\",\n                        children: isFlowMode ? 'Exit Flow' : 'Flow Mode'\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/flow/app/components/AudioPlayer.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/flow/app/components/AudioPlayer.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Projects/flow/app/components/AudioPlayer.tsx\",\n        lineNumber: 72,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/AudioPlayer.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/DynamicBackground.tsx":
/*!**********************************************!*\
  !*** ./app/components/DynamicBackground.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DynamicBackground)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(ssr)/./node_modules/next/dist/api/app-dynamic.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n// Dynamically import the entire Three.js component to prevent SSR issues\nconst ThreeJSBackground = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(async ()=>{\n     true && /*require.resolve*/(null /* weak dependency, without id */);\n}, {\n    loadableGenerated: {\n        modules: [\n            \"app/components/DynamicBackground.tsx -> \" + \"./ThreeJSBackground\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"absolute inset-0 gradient-bg\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Projects/flow/app/components/DynamicBackground.tsx\",\n            lineNumber: 9,\n            columnNumber: 18\n        }, undefined)\n});\nfunction DynamicBackground({ prompt }) {\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"absolute inset-0 gradient-bg\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Projects/flow/app/components/DynamicBackground.tsx\",\n            lineNumber: 26,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"absolute inset-0 gradient-bg\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThreeJSBackground, {\n            prompt: prompt\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Projects/flow/app/components/DynamicBackground.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Projects/flow/app/components/DynamicBackground.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/DynamicBackground.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/PomodoroTimer.tsx":
/*!******************************************!*\
  !*** ./app/components/PomodoroTimer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PomodoroTimer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction PomodoroTimer({ onSessionComplete }) {\n    const [timeLeft, setTimeLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(25 * 60) // 25 minutes in seconds\n    ;\n    const [isActive, setIsActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isBreak, setIsBreak] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sessions, setSessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let interval = null;\n        if (isActive && timeLeft > 0) {\n            interval = setInterval(()=>{\n                setTimeLeft((timeLeft)=>timeLeft - 1);\n            }, 1000);\n        } else if (timeLeft === 0) {\n            // Session complete\n            if (isBreak) {\n                setTimeLeft(25 * 60) // Back to work\n                ;\n                setIsBreak(false);\n            } else {\n                setSessions((prev)=>prev + 1);\n                setTimeLeft(5 * 60) // 5 minute break\n                ;\n                setIsBreak(true);\n            }\n            setIsActive(false);\n            onSessionComplete?.();\n        }\n        return ()=>{\n            if (interval) clearInterval(interval);\n        };\n    }, [\n        isActive,\n        timeLeft,\n        isBreak,\n        onSessionComplete\n    ]);\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n    };\n    const resetTimer = ()=>{\n        setIsActive(false);\n        setTimeLeft(25 * 60);\n        setIsBreak(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n        initial: {\n            opacity: 0,\n            scale: 0.9\n        },\n        animate: {\n            opacity: 1,\n            scale: 1\n        },\n        className: \"glass-effect p-4 rounded-lg\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-sm font-medium text-gray-300 mb-2\",\n                    children: isBreak ? 'Break Time' : 'Focus Session'\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/flow/app/components/PomodoroTimer.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-2xl font-mono font-bold mb-4\",\n                    children: formatTime(timeLeft)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/flow/app/components/PomodoroTimer.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-2 justify-center mb-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsActive(!isActive),\n                            className: \"bg-flow-accent hover:bg-flow-accent/80 text-white px-4 py-2 rounded text-sm transition-colors\",\n                            children: isActive ? 'Pause' : 'Start'\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/flow/app/components/PomodoroTimer.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: resetTimer,\n                            className: \"bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded text-sm transition-colors\",\n                            children: \"Reset\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/flow/app/components/PomodoroTimer.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Projects/flow/app/components/PomodoroTimer.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs text-gray-400\",\n                    children: [\n                        \"Sessions completed: \",\n                        sessions\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Projects/flow/app/components/PomodoroTimer.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Projects/flow/app/components/PomodoroTimer.tsx\",\n            lineNumber: 60,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Projects/flow/app/components/PomodoroTimer.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/PomodoroTimer.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/ProductivityPanel.tsx":
/*!**********************************************!*\
  !*** ./app/components/ProductivityPanel.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductivityPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _PomodoroTimer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./PomodoroTimer */ \"(ssr)/./app/components/PomodoroTimer.tsx\");\n/* harmony import */ var _TodoList__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./TodoList */ \"(ssr)/./app/components/TodoList.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction ProductivityPanel() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        className: \"fixed bottom-6 left-6 right-6 flex gap-4 justify-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PomodoroTimer__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/flow/app/components/ProductivityPanel.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TodoList__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/flow/app/components/ProductivityPanel.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Projects/flow/app/components/ProductivityPanel.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9Qcm9kdWN0aXZpdHlQYW5lbC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUVzQztBQUNLO0FBQ1Y7QUFFbEIsU0FBU0c7SUFDdEIscUJBQ0UsOERBQUNILGlEQUFNQSxDQUFDSSxHQUFHO1FBQ1RDLFNBQVM7WUFBRUMsU0FBUztZQUFHQyxHQUFHO1FBQUc7UUFDN0JDLFNBQVM7WUFBRUYsU0FBUztZQUFHQyxHQUFHO1FBQUU7UUFDNUJFLFdBQVU7OzBCQUVWLDhEQUFDUixzREFBYUE7Ozs7OzBCQUNkLDhEQUFDQyxpREFBUUE7Ozs7Ozs7Ozs7O0FBR2YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2RlZmxvdy8uL2FwcC9jb21wb25lbnRzL1Byb2R1Y3Rpdml0eVBhbmVsLnRzeD9iMDUyIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyBtb3Rpb24gfSBmcm9tICdmcmFtZXItbW90aW9uJ1xuaW1wb3J0IFBvbW9kb3JvVGltZXIgZnJvbSAnLi9Qb21vZG9yb1RpbWVyJ1xuaW1wb3J0IFRvZG9MaXN0IGZyb20gJy4vVG9kb0xpc3QnXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFByb2R1Y3Rpdml0eVBhbmVsKCkge1xuICByZXR1cm4gKFxuICAgIDxtb3Rpb24uZGl2IFxuICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxuICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICBjbGFzc05hbWU9XCJmaXhlZCBib3R0b20tNiBsZWZ0LTYgcmlnaHQtNiBmbGV4IGdhcC00IGp1c3RpZnktY2VudGVyXCJcbiAgICA+XG4gICAgICA8UG9tb2Rvcm9UaW1lciAvPlxuICAgICAgPFRvZG9MaXN0IC8+XG4gICAgPC9tb3Rpb24uZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsibW90aW9uIiwiUG9tb2Rvcm9UaW1lciIsIlRvZG9MaXN0IiwiUHJvZHVjdGl2aXR5UGFuZWwiLCJkaXYiLCJpbml0aWFsIiwib3BhY2l0eSIsInkiLCJhbmltYXRlIiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ProductivityPanel.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/PromptInput.tsx":
/*!****************************************!*\
  !*** ./app/components/PromptInput.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PromptInput)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction PromptInput({ onGenerate, isGenerating }) {\n    const [prompt, setPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (prompt.trim() && !isGenerating) {\n            onGenerate(prompt.trim());\n        }\n    };\n    const presetPrompts = [\n        'Rainy day coding session',\n        'Cyberpunk neon vibes',\n        'Forest morning ambience',\n        'Retro 8-bit soundscape',\n        'Deep focus meditation'\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        className: \"glass-effect p-8 rounded-lg max-w-md w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-2xl font-bold text-center mb-6\",\n                children: \"Generate Your Flow\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/flow/app/components/PromptInput.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"prompt\",\n                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                children: \"Describe your ideal coding atmosphere\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/flow/app/components/PromptInput.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                id: \"prompt\",\n                                value: prompt,\n                                onChange: (e)=>setPrompt(e.target.value),\n                                placeholder: \"e.g., Peaceful rain sounds with soft piano for deep focus...\",\n                                className: \"w-full p-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 resize-none\",\n                                rows: 3,\n                                disabled: isGenerating\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/flow/app/components/PromptInput.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/flow/app/components/PromptInput.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"submit\",\n                        disabled: !prompt.trim() || isGenerating,\n                        className: \"w-full bg-flow-accent hover:bg-flow-accent/80 disabled:bg-gray-600 disabled:cursor-not-allowed text-white py-3 px-6 rounded-lg font-medium transition-colors\",\n                        children: isGenerating ? 'Generating Flow...' : 'Generate Flow'\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/flow/app/components/PromptInput.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/flow/app/components/PromptInput.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-400 mb-3\",\n                        children: \"Or try a preset:\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/flow/app/components/PromptInput.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: presetPrompts.map((preset, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setPrompt(preset),\n                                disabled: isGenerating,\n                                className: \"w-full text-left p-2 text-sm bg-white/5 hover:bg-white/10 disabled:hover:bg-white/5 rounded border border-white/10 transition-colors\",\n                                children: preset\n                            }, index, false, {\n                                fileName: \"/Users/<USER>/Projects/flow/app/components/PromptInput.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/flow/app/components/PromptInput.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/flow/app/components/PromptInput.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Projects/flow/app/components/PromptInput.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/PromptInput.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/TodoList.tsx":
/*!*************************************!*\
  !*** ./app/components/TodoList.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TodoList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction TodoList() {\n    const [todos, setTodos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [newTodo, setNewTodo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const addTodo = (e)=>{\n        e.preventDefault();\n        if (newTodo.trim()) {\n            setTodos([\n                ...todos,\n                {\n                    id: Date.now(),\n                    text: newTodo.trim(),\n                    completed: false\n                }\n            ]);\n            setNewTodo('');\n        }\n    };\n    const toggleTodo = (id)=>{\n        setTodos(todos.map((todo)=>todo.id === id ? {\n                ...todo,\n                completed: !todo.completed\n            } : todo));\n    };\n    const deleteTodo = (id)=>{\n        setTodos(todos.filter((todo)=>todo.id !== id));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n        initial: {\n            opacity: 0,\n            scale: 0.9\n        },\n        animate: {\n            opacity: 1,\n            scale: 1\n        },\n        className: \"glass-effect p-4 rounded-lg w-80\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-sm font-medium text-gray-300 mb-3\",\n                children: \"Session Tasks\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/flow/app/components/TodoList.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: addTodo,\n                className: \"mb-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: newTodo,\n                            onChange: (e)=>setNewTodo(e.target.value),\n                            placeholder: \"Add a task...\",\n                            className: \"flex-1 p-2 text-sm bg-white/10 border border-white/20 rounded text-white placeholder-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/flow/app/components/TodoList.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            className: \"bg-flow-accent hover:bg-flow-accent/80 text-white px-3 py-2 rounded text-sm transition-colors\",\n                            children: \"Add\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/flow/app/components/TodoList.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Projects/flow/app/components/TodoList.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/flow/app/components/TodoList.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2 max-h-40 overflow-y-auto\",\n                children: todos.map((todo)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 p-2 bg-white/5 rounded\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"checkbox\",\n                                checked: todo.completed,\n                                onChange: ()=>toggleTodo(todo.id),\n                                className: \"rounded\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/flow/app/components/TodoList.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: `flex-1 text-sm ${todo.completed ? 'line-through text-gray-400' : 'text-white'}`,\n                                children: todo.text\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/flow/app/components/TodoList.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>deleteTodo(todo.id),\n                                className: \"text-red-400 hover:text-red-300 text-sm\",\n                                children: \"\\xd7\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/flow/app/components/TodoList.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, todo.id, true, {\n                        fileName: \"/Users/<USER>/Projects/flow/app/components/TodoList.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/flow/app/components/TodoList.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this),\n            todos.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-400 text-sm text-center py-4\",\n                children: \"No tasks yet. Add one above!\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/flow/app/components/TodoList.tsx\",\n                lineNumber: 94,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Projects/flow/app/components/TodoList.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/TodoList.tsx\n");

/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_PromptInput__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/PromptInput */ \"(ssr)/./app/components/PromptInput.tsx\");\n/* harmony import */ var _components_DynamicBackground__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/DynamicBackground */ \"(ssr)/./app/components/DynamicBackground.tsx\");\n/* harmony import */ var _components_AudioPlayer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/AudioPlayer */ \"(ssr)/./app/components/AudioPlayer.tsx\");\n/* harmony import */ var _components_ProductivityPanel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/ProductivityPanel */ \"(ssr)/./app/components/ProductivityPanel.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction Home() {\n    const [currentPrompt, setCurrentPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [audioUrl, setAudioUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isFlowMode, setIsFlowMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleGenerateFlow = async (prompt)=>{\n        setCurrentPrompt(prompt);\n        setIsGenerating(true);\n        try {\n            const response = await fetch('/api/generate-audio', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    prompt\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to generate audio');\n            }\n            const data = await response.json();\n            setAudioUrl(data.audioUrl);\n        } catch (error) {\n            console.error('Failed to generate flow:', error);\n        // Show error to user\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"relative h-screen w-full overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DynamicBackground__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                prompt: currentPrompt\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/flow/app/page.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 h-full flex flex-col\",\n                children: [\n                    !isFlowMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-center mb-8\",\n                            children: [\n                                \"Code\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-flow-accent\",\n                                    children: \"Flow\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/flow/app/page.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/flow/app/page.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/flow/app/page.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex items-center justify-center px-6\",\n                        children: !audioUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PromptInput__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            onGenerate: handleGenerateFlow,\n                            isGenerating: isGenerating\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/flow/app/page.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AudioPlayer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            audioUrl: audioUrl,\n                            prompt: currentPrompt,\n                            onNewFlow: ()=>setAudioUrl(null),\n                            isFlowMode: isFlowMode,\n                            onToggleFlowMode: ()=>setIsFlowMode(!isFlowMode)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/flow/app/page.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/flow/app/page.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    !isFlowMode && audioUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProductivityPanel__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/flow/app/page.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/flow/app/page.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Projects/flow/app/page.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"ba5d14ac4a04\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2RlZmxvdy8uL2FwcC9nbG9iYWxzLmNzcz8wYTAzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiYmE1ZDE0YWM0YTA0XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\nconst metadata = {\n    title: 'CodeFlow - AI-Powered Ambient Coding Environment',\n    description: 'Get into flow state with AI-generated soundscapes and dynamic visuals designed for programmers.'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"dark\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className)} bg-flow-dark text-white antialiased`,\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Projects/flow/app/layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Projects/flow/app/layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUlNQTtBQUZnQjtBQUlmLE1BQU1DLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7UUFBS0MsV0FBVTtrQkFDeEIsNEVBQUNDO1lBQUtELFdBQVcsR0FBR1IsMkpBQWUsQ0FBQyxvQ0FBb0MsQ0FBQztzQkFDdEVLOzs7Ozs7Ozs7OztBQUlUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29kZWZsb3cvLi9hcHAvbGF5b3V0LnRzeD85OTg4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJ1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogWydsYXRpbiddIH0pXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnQ29kZUZsb3cgLSBBSS1Qb3dlcmVkIEFtYmllbnQgQ29kaW5nIEVudmlyb25tZW50JyxcbiAgZGVzY3JpcHRpb246ICdHZXQgaW50byBmbG93IHN0YXRlIHdpdGggQUktZ2VuZXJhdGVkIHNvdW5kc2NhcGVzIGFuZCBkeW5hbWljIHZpc3VhbHMgZGVzaWduZWQgZm9yIHByb2dyYW1tZXJzLicsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiIGNsYXNzTmFtZT1cImRhcmtcIj5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT17YCR7aW50ZXIuY2xhc3NOYW1lfSBiZy1mbG93LWRhcmsgdGV4dC13aGl0ZSBhbnRpYWxpYXNlZGB9PlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59Il0sIm5hbWVzIjpbImludGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJjbGFzc05hbWUiLCJib2R5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const proxy = await (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Projects/flow/app/page.tsx`)
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (proxy.default);

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } }, 1);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fcoryjanowski%2FProjects%2Fflow&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();