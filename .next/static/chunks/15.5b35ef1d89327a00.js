"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[15],{5015:(e,t,n)=>{n.r(t),n.d(t,{default:()=>o});var r=n(7437),a=n(2265),i=n(2948),s=n(6777);function c(e){let{prompt:t}=e,n=(0,a.useRef)(null),s=new Float32Array(15e3);for(let e=0;e<5e3;e++){let t=2*Math.random(),n=Math.random()*Math.PI*2,r=Math.acos(2*Math.random()-1);s[3*e]=t*Math.sin(r)*Math.cos(n),s[3*e+1]=t*Math.sin(r)*Math.sin(n),s[3*e+2]=t*Math.cos(r)}return(0,i.F)(e=>{n.current&&(n.current.rotation.x=.1*Math.sin(.1*e.clock.elapsedTime),n.current.rotation.y=.05*e.clock.elapsedTime)}),(0,r.jsxs)("points",{ref:n,children:[(0,r.jsx)("bufferGeometry",{children:(0,r.jsx)("bufferAttribute",{attach:"attributes-position",count:s.length/3,array:s,itemSize:3})}),(0,r.jsx)("pointsMaterial",{transparent:!0,color:t.includes("rain")||t.includes("calm")?"#4f46e5":t.includes("cyber")||t.includes("neon")?"#06ffa5":t.includes("forest")||t.includes("nature")?"#10b981":t.includes("retro")||t.includes("8-bit")?"#f59e0b":"#6366f1",size:.002,sizeAttenuation:!0,depthWrite:!1})]})}function o(e){let{prompt:t}=e;return(0,r.jsx)(s.Xz,{camera:{position:[0,0,1]},children:(0,r.jsx)(c,{prompt:t})})}}}]);