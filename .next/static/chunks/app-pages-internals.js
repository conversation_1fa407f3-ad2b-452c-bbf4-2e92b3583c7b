/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app-pages-internals"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/lib/metadata/metadata-boundary.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    MetadataBoundary: function() {\n        return MetadataBoundary;\n    },\n    OutletBoundary: function() {\n        return OutletBoundary;\n    },\n    ViewportBoundary: function() {\n        return ViewportBoundary;\n    }\n});\nconst _metadataconstants = __webpack_require__(/*! ./metadata-constants */ \"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-constants.js\");\n// We use a namespace object to allow us to recover the name of the function\n// at runtime even when production bundling/minification is used.\nconst NameSpace = {\n    [_metadataconstants.METADATA_BOUNDARY_NAME]: function(param) {\n        let { children } = param;\n        return children;\n    },\n    [_metadataconstants.VIEWPORT_BOUNDARY_NAME]: function(param) {\n        let { children } = param;\n        return children;\n    },\n    [_metadataconstants.OUTLET_BOUNDARY_NAME]: function(param) {\n        let { children } = param;\n        return children;\n    }\n};\nconst MetadataBoundary = // so it retains the name inferred from the namespace object\nNameSpace[_metadataconstants.METADATA_BOUNDARY_NAME.slice(0)];\nconst ViewportBoundary = // so it retains the name inferred from the namespace object\nNameSpace[_metadataconstants.VIEWPORT_BOUNDARY_NAME.slice(0)];\nconst OutletBoundary = // so it retains the name inferred from the namespace object\nNameSpace[_metadataconstants.OUTLET_BOUNDARY_NAME.slice(0)]; //# sourceMappingURL=metadata-boundary.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/create-deduped-by-callsite-server-error-loger.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/server/create-deduped-by-callsite-server-error-loger.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"createDedupedByCallsiteServerErrorLoggerDev\", ({\n    enumerable: true,\n    get: function() {\n        return createDedupedByCallsiteServerErrorLoggerDev;\n    }\n}));\nconst _react = /*#__PURE__*/ _interop_require_wildcard(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interop_require_wildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) {\n        return obj;\n    }\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n        return {\n            default: obj\n        };\n    }\n    var cache = _getRequireWildcardCache(nodeInterop);\n    if (cache && cache.has(obj)) {\n        return cache.get(obj);\n    }\n    var newObj = {\n        __proto__: null\n    };\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n    for(var key in obj){\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) {\n                Object.defineProperty(newObj, key, desc);\n            } else {\n                newObj[key] = obj[key];\n            }\n        }\n    }\n    newObj.default = obj;\n    if (cache) {\n        cache.set(obj, newObj);\n    }\n    return newObj;\n}\nconst errorRef = {\n    current: null\n};\n// React.cache is currently only available in canary/experimental React channels.\nconst cache = typeof _react.cache === 'function' ? _react.cache : (fn)=>fn;\n// We don't want to dedupe across requests.\n// The developer might've just attempted to fix the warning so we should warn again if it still happens.\nconst flushCurrentErrorIfNew = cache(// eslint-disable-next-line @typescript-eslint/no-unused-vars -- cache key\n(key)=>{\n    try {\n        console.error(errorRef.current);\n    } finally{\n        errorRef.current = null;\n    }\n});\nfunction createDedupedByCallsiteServerErrorLoggerDev(getMessage) {\n    return function logDedupedError(...args) {\n        const message = getMessage(...args);\n        if (true) {\n            var _stack;\n            const callStackFrames = (_stack = new Error().stack) == null ? void 0 : _stack.split('\\n');\n            if (callStackFrames === undefined || callStackFrames.length < 4) {\n                console.error(message);\n            } else {\n                // Error:\n                //   logDedupedError\n                //   asyncApiBeingAccessedSynchronously\n                //   <userland callsite>\n                // TODO: This breaks if sourcemaps with ignore lists are enabled.\n                const key = callStackFrames[3];\n                errorRef.current = message;\n                flushCurrentErrorIfNew(key);\n            }\n        } else {}\n    };\n}\n\n//# sourceMappingURL=create-deduped-by-callsite-server-error-loger.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/create-deduped-by-callsite-server-error-loger.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/request/params.browser.js":
/*!*****************************************************************!*\
  !*** ./node_modules/next/dist/server/request/params.browser.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"createRenderParamsFromClient\", ({\n    enumerable: true,\n    get: function() {\n        return createRenderParamsFromClient;\n    }\n}));\nconst _reflect = __webpack_require__(/*! ../web/spec-extension/adapters/reflect */ \"(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/adapters/reflect.js\");\nconst _invarianterror = __webpack_require__(/*! ../../shared/lib/invariant-error */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/invariant-error.js\");\nconst _utils = __webpack_require__(/*! ./utils */ \"(app-pages-browser)/./node_modules/next/dist/server/request/utils.js\");\nfunction createRenderParamsFromClient(underlyingParams) {\n    if (true) {\n        return makeDynamicallyTrackedExoticParamsWithDevWarnings(underlyingParams);\n    } else {}\n}\nconst CachedParams = new WeakMap();\nfunction makeUntrackedExoticParams(underlyingParams) {\n    const cachedParams = CachedParams.get(underlyingParams);\n    if (cachedParams) {\n        return cachedParams;\n    }\n    const promise = Promise.resolve(underlyingParams);\n    CachedParams.set(underlyingParams, promise);\n    Object.keys(underlyingParams).forEach((prop)=>{\n        if (_utils.wellKnownProperties.has(prop)) {\n        // These properties cannot be shadowed because they need to be the\n        // true underlying value for Promises to work correctly at runtime\n        } else {\n            promise[prop] = underlyingParams[prop];\n        }\n    });\n    return promise;\n}\nfunction makeDynamicallyTrackedExoticParamsWithDevWarnings(underlyingParams) {\n    const cachedParams = CachedParams.get(underlyingParams);\n    if (cachedParams) {\n        return cachedParams;\n    }\n    // We don't use makeResolvedReactPromise here because params\n    // supports copying with spread and we don't want to unnecessarily\n    // instrument the promise with spreadable properties of ReactPromise.\n    const promise = Promise.resolve(underlyingParams);\n    const proxiedProperties = new Set();\n    const unproxiedProperties = [];\n    Object.keys(underlyingParams).forEach((prop)=>{\n        if (_utils.wellKnownProperties.has(prop)) {\n        // These properties cannot be shadowed because they need to be the\n        // true underlying value for Promises to work correctly at runtime\n        } else {\n            proxiedProperties.add(prop);\n            promise[prop] = underlyingParams[prop];\n        }\n    });\n    const proxiedPromise = new Proxy(promise, {\n        get (target, prop, receiver) {\n            if (typeof prop === 'string') {\n                if (// We are accessing a property that was proxied to the promise instance\n                proxiedProperties.has(prop)) {\n                    const expression = (0, _utils.describeStringPropertyAccess)('params', prop);\n                    warnForSyncAccess(expression);\n                }\n            }\n            return _reflect.ReflectAdapter.get(target, prop, receiver);\n        },\n        set (target, prop, value, receiver) {\n            if (typeof prop === 'string') {\n                proxiedProperties.delete(prop);\n            }\n            return _reflect.ReflectAdapter.set(target, prop, value, receiver);\n        },\n        ownKeys (target) {\n            warnForEnumeration(unproxiedProperties);\n            return Reflect.ownKeys(target);\n        }\n    });\n    CachedParams.set(underlyingParams, proxiedPromise);\n    return proxiedPromise;\n}\nconst noop = ()=>{};\nconst warnForSyncAccess =  false ? 0 : function warnForSyncAccess(expression) {\n    if (false) {}\n    console.error(`A param property was accessed directly with ${expression}. \\`params\\` is now a Promise and should be unwrapped with \\`React.use()\\` before accessing properties of the underlying params object. In this version of Next.js direct access to param properties is still supported to facilitate migration but in a future version you will be required to unwrap \\`params\\` with \\`React.use()\\`.`);\n};\nconst warnForEnumeration =  false ? 0 : function warnForEnumeration(missingProperties) {\n    if (false) {}\n    if (missingProperties.length) {\n        const describedMissingProperties = describeListOfPropertyNames(missingProperties);\n        console.error(`params are being enumerated incompletely missing these properties: ${describedMissingProperties}. ` + `\\`params\\` should be unwrapped with \\`React.use()\\` before using its value. ` + `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`);\n    } else {\n        console.error(`params are being enumerated. ` + `\\`params\\` should be unwrapped with \\`React.use()\\` before using its value. ` + `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`);\n    }\n};\nfunction describeListOfPropertyNames(properties) {\n    switch(properties.length){\n        case 0:\n            throw new _invarianterror.InvariantError('Expected describeListOfPropertyNames to be called with a non-empty list of strings.');\n        case 1:\n            return `\\`${properties[0]}\\``;\n        case 2:\n            return `\\`${properties[0]}\\` and \\`${properties[1]}\\``;\n        default:\n            {\n                let description = '';\n                for(let i = 0; i < properties.length - 1; i++){\n                    description += `\\`${properties[i]}\\`, `;\n                }\n                description += `, and \\`${properties[properties.length - 1]}\\``;\n                return description;\n            }\n    }\n}\n\n//# sourceMappingURL=params.browser.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/request/params.browser.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/request/params.js":
/*!*********************************************************!*\
  !*** ./node_modules/next/dist/server/request/params.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createPrerenderParamsForClientSegment: function() {\n        return createPrerenderParamsForClientSegment;\n    },\n    createPrerenderParamsFromClient: function() {\n        return createPrerenderParamsFromClient;\n    },\n    createRenderParamsFromClient: function() {\n        return createRenderParamsFromClient;\n    },\n    createServerParamsForMetadata: function() {\n        return createServerParamsForMetadata;\n    },\n    createServerParamsForRoute: function() {\n        return createServerParamsForRoute;\n    },\n    createServerParamsForServerSegment: function() {\n        return createServerParamsForServerSegment;\n    }\n});\nconst _reflect = __webpack_require__(/*! ../web/spec-extension/adapters/reflect */ \"(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/adapters/reflect.js\");\nconst _dynamicrendering = __webpack_require__(/*! ../app-render/dynamic-rendering */ \"(app-pages-browser)/./node_modules/next/dist/server/app-render/dynamic-rendering.js\");\nconst _workunitasyncstorageexternal = __webpack_require__(/*! ../app-render/work-unit-async-storage.external */ \"(shared)/./node_modules/next/dist/server/app-render/work-unit-async-storage.external.js\");\nconst _invarianterror = __webpack_require__(/*! ../../shared/lib/invariant-error */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/invariant-error.js\");\nconst _utils = __webpack_require__(/*! ./utils */ \"(app-pages-browser)/./node_modules/next/dist/server/request/utils.js\");\nconst _dynamicrenderingutils = __webpack_require__(/*! ../dynamic-rendering-utils */ \"(app-pages-browser)/./node_modules/next/dist/server/dynamic-rendering-utils.js\");\nconst _creatededupedbycallsiteservererrorloger = __webpack_require__(/*! ../create-deduped-by-callsite-server-error-loger */ \"(app-pages-browser)/./node_modules/next/dist/server/create-deduped-by-callsite-server-error-loger.js\");\nfunction createPrerenderParamsFromClient(underlyingParams, workStore) {\n    return createPrerenderParams(underlyingParams, workStore);\n}\nfunction createRenderParamsFromClient(underlyingParams, workStore) {\n    return createRenderParams(underlyingParams, workStore);\n}\nconst createServerParamsForMetadata = createServerParamsForServerSegment;\nfunction createServerParamsForRoute(underlyingParams, workStore) {\n    if (workStore.isStaticGeneration) {\n        return createPrerenderParams(underlyingParams, workStore);\n    } else {\n        return createRenderParams(underlyingParams, workStore);\n    }\n}\nfunction createServerParamsForServerSegment(underlyingParams, workStore) {\n    if (workStore.isStaticGeneration) {\n        return createPrerenderParams(underlyingParams, workStore);\n    } else {\n        return createRenderParams(underlyingParams, workStore);\n    }\n}\nfunction createPrerenderParamsForClientSegment(underlyingParams, workStore) {\n    const prerenderStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (prerenderStore && prerenderStore.type === 'prerender') {\n        const fallbackParams = workStore.fallbackRouteParams;\n        if (fallbackParams) {\n            for(let key in underlyingParams){\n                if (fallbackParams.has(key)) {\n                    // This params object has one of more fallback params so we need to consider\n                    // the awaiting of this params object \"dynamic\". Since we are in dynamicIO mode\n                    // we encode this as a promise that never resolves\n                    return (0, _dynamicrenderingutils.makeHangingPromise)(prerenderStore.renderSignal, '`params`');\n                }\n            }\n        }\n    }\n    // We're prerendering in a mode that does not abort. We resolve the promise without\n    // any tracking because we're just transporting a value from server to client where the tracking\n    // will be applied.\n    return (0, _utils.makeResolvedReactPromise)(underlyingParams);\n}\nfunction createPrerenderParams(underlyingParams, workStore) {\n    const fallbackParams = workStore.fallbackRouteParams;\n    if (fallbackParams) {\n        let hasSomeFallbackParams = false;\n        for(const key in underlyingParams){\n            if (fallbackParams.has(key)) {\n                hasSomeFallbackParams = true;\n                break;\n            }\n        }\n        if (hasSomeFallbackParams) {\n            // params need to be treated as dynamic because we have at least one fallback param\n            const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n            if (workUnitStore) {\n                if (workUnitStore.type === 'prerender') {\n                    // We are in a dynamicIO (PPR or otherwise) prerender\n                    return makeAbortingExoticParams(underlyingParams, workStore.route, workUnitStore);\n                } else if (workUnitStore.type === 'prerender-legacy' || workUnitStore.type === 'prerender-ppr') // We aren't in a dynamicIO prerender but we do have fallback params at this\n                // level so we need to make an erroring exotic params object which will postpone\n                // if you access the fallback params\n                return makeErroringExoticParams(underlyingParams, fallbackParams, workStore, workUnitStore);\n            }\n            throw new _invarianterror.InvariantError('createPrerenderParams called without a prerenderStore in scope. This is a bug in Next.js');\n        }\n    }\n    // We don't have any fallback params so we have an entirely static safe params object\n    return makeUntrackedExoticParams(underlyingParams);\n}\nfunction createRenderParams(underlyingParams, workStore) {\n    if ( true && !workStore.isPrefetchRequest) {\n        return makeDynamicallyTrackedExoticParamsWithDevWarnings(underlyingParams, workStore);\n    } else {\n        return makeUntrackedExoticParams(underlyingParams);\n    }\n}\nconst CachedParams = new WeakMap();\nfunction makeAbortingExoticParams(underlyingParams, route, prerenderStore) {\n    const cachedParams = CachedParams.get(underlyingParams);\n    if (cachedParams) {\n        return cachedParams;\n    }\n    const promise = (0, _dynamicrenderingutils.makeHangingPromise)(prerenderStore.renderSignal, '`params`');\n    CachedParams.set(underlyingParams, promise);\n    Object.keys(underlyingParams).forEach((prop)=>{\n        if (_utils.wellKnownProperties.has(prop)) {\n        // These properties cannot be shadowed because they need to be the\n        // true underlying value for Promises to work correctly at runtime\n        } else {\n            Object.defineProperty(promise, prop, {\n                get () {\n                    const expression = (0, _utils.describeStringPropertyAccess)('params', prop);\n                    const error = new Error(`Route \"${route}\" used ${expression}. \\`params\\` is now a Promise and should be \\`awaited\\` before accessing param values. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-params`);\n                    (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n                },\n                set (newValue) {\n                    Object.defineProperty(promise, prop, {\n                        value: newValue,\n                        writable: true,\n                        enumerable: true\n                    });\n                },\n                enumerable: true,\n                configurable: true\n            });\n        }\n    });\n    return promise;\n}\nfunction makeErroringExoticParams(underlyingParams, fallbackParams, workStore, prerenderStore) {\n    const cachedParams = CachedParams.get(underlyingParams);\n    if (cachedParams) {\n        return cachedParams;\n    }\n    const augmentedUnderlying = {\n        ...underlyingParams\n    };\n    // We don't use makeResolvedReactPromise here because params\n    // supports copying with spread and we don't want to unnecessarily\n    // instrument the promise with spreadable properties of ReactPromise.\n    const promise = Promise.resolve(augmentedUnderlying);\n    CachedParams.set(underlyingParams, promise);\n    Object.keys(underlyingParams).forEach((prop)=>{\n        if (_utils.wellKnownProperties.has(prop)) {\n        // These properties cannot be shadowed because they need to be the\n        // true underlying value for Promises to work correctly at runtime\n        } else {\n            if (fallbackParams.has(prop)) {\n                Object.defineProperty(augmentedUnderlying, prop, {\n                    get () {\n                        const expression = (0, _utils.describeStringPropertyAccess)('params', prop);\n                        // In most dynamic APIs we also throw if `dynamic = \"error\"` however\n                        // for params is only dynamic when we're generating a fallback shell\n                        // and even when `dynamic = \"error\"` we still support generating dynamic\n                        // fallback shells\n                        // TODO remove this comment when dynamicIO is the default since there\n                        // will be no `dynamic = \"error\"`\n                        if (prerenderStore.type === 'prerender-ppr') {\n                            // PPR Prerender (no dynamicIO)\n                            (0, _dynamicrendering.postponeWithTracking)(workStore.route, expression, prerenderStore.dynamicTracking);\n                        } else {\n                            // Legacy Prerender\n                            (0, _dynamicrendering.throwToInterruptStaticGeneration)(expression, workStore, prerenderStore);\n                        }\n                    },\n                    enumerable: true\n                });\n                Object.defineProperty(promise, prop, {\n                    get () {\n                        const expression = (0, _utils.describeStringPropertyAccess)('params', prop);\n                        // In most dynamic APIs we also throw if `dynamic = \"error\"` however\n                        // for params is only dynamic when we're generating a fallback shell\n                        // and even when `dynamic = \"error\"` we still support generating dynamic\n                        // fallback shells\n                        // TODO remove this comment when dynamicIO is the default since there\n                        // will be no `dynamic = \"error\"`\n                        if (prerenderStore.type === 'prerender-ppr') {\n                            // PPR Prerender (no dynamicIO)\n                            (0, _dynamicrendering.postponeWithTracking)(workStore.route, expression, prerenderStore.dynamicTracking);\n                        } else {\n                            // Legacy Prerender\n                            (0, _dynamicrendering.throwToInterruptStaticGeneration)(expression, workStore, prerenderStore);\n                        }\n                    },\n                    set (newValue) {\n                        Object.defineProperty(promise, prop, {\n                            value: newValue,\n                            writable: true,\n                            enumerable: true\n                        });\n                    },\n                    enumerable: true,\n                    configurable: true\n                });\n            } else {\n                promise[prop] = underlyingParams[prop];\n            }\n        }\n    });\n    return promise;\n}\nfunction makeUntrackedExoticParams(underlyingParams) {\n    const cachedParams = CachedParams.get(underlyingParams);\n    if (cachedParams) {\n        return cachedParams;\n    }\n    // We don't use makeResolvedReactPromise here because params\n    // supports copying with spread and we don't want to unnecessarily\n    // instrument the promise with spreadable properties of ReactPromise.\n    const promise = Promise.resolve(underlyingParams);\n    CachedParams.set(underlyingParams, promise);\n    Object.keys(underlyingParams).forEach((prop)=>{\n        if (_utils.wellKnownProperties.has(prop)) {\n        // These properties cannot be shadowed because they need to be the\n        // true underlying value for Promises to work correctly at runtime\n        } else {\n            promise[prop] = underlyingParams[prop];\n        }\n    });\n    return promise;\n}\nfunction makeDynamicallyTrackedExoticParamsWithDevWarnings(underlyingParams, store) {\n    const cachedParams = CachedParams.get(underlyingParams);\n    if (cachedParams) {\n        return cachedParams;\n    }\n    // We don't use makeResolvedReactPromise here because params\n    // supports copying with spread and we don't want to unnecessarily\n    // instrument the promise with spreadable properties of ReactPromise.\n    const promise = Promise.resolve(underlyingParams);\n    const proxiedProperties = new Set();\n    const unproxiedProperties = [];\n    Object.keys(underlyingParams).forEach((prop)=>{\n        if (_utils.wellKnownProperties.has(prop)) {\n            // These properties cannot be shadowed because they need to be the\n            // true underlying value for Promises to work correctly at runtime\n            unproxiedProperties.push(prop);\n        } else {\n            proxiedProperties.add(prop);\n            promise[prop] = underlyingParams[prop];\n        }\n    });\n    const proxiedPromise = new Proxy(promise, {\n        get (target, prop, receiver) {\n            if (typeof prop === 'string') {\n                if (// We are accessing a property that was proxied to the promise instance\n                proxiedProperties.has(prop)) {\n                    const expression = (0, _utils.describeStringPropertyAccess)('params', prop);\n                    warnForSyncAccess(store.route, expression);\n                }\n            }\n            return _reflect.ReflectAdapter.get(target, prop, receiver);\n        },\n        set (target, prop, value, receiver) {\n            if (typeof prop === 'string') {\n                proxiedProperties.delete(prop);\n            }\n            return _reflect.ReflectAdapter.set(target, prop, value, receiver);\n        },\n        ownKeys (target) {\n            warnForEnumeration(store.route, unproxiedProperties);\n            return Reflect.ownKeys(target);\n        }\n    });\n    CachedParams.set(underlyingParams, proxiedPromise);\n    return proxiedPromise;\n}\nconst noop = ()=>{};\nconst warnForSyncAccess =  false ? 0 : (0, _creatededupedbycallsiteservererrorloger.createDedupedByCallsiteServerErrorLoggerDev)(function getSyncAccessMessage(route, expression) {\n    const prefix = route ? ` In route ${route} a ` : 'A ';\n    return new Error(`${prefix}param property was accessed directly with ${expression}. ` + `\\`params\\` should be awaited before accessing its properties. ` + `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`);\n});\nconst warnForEnumeration =  false ? 0 : (0, _creatededupedbycallsiteservererrorloger.createDedupedByCallsiteServerErrorLoggerDev)(function getEnumerationMessage(route, missingProperties) {\n    const prefix = route ? ` In route ${route} ` : '';\n    if (missingProperties.length) {\n        const describedMissingProperties = describeListOfPropertyNames(missingProperties);\n        return new Error(`${prefix}params are being enumerated incompletely missing these properties: ${describedMissingProperties}. ` + `\\`params\\` should be awaited before accessing its properties. ` + `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`);\n    } else {\n        return new Error(`${prefix}params are being enumerated. ` + `\\`params\\` should be awaited before accessing its properties. ` + `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`);\n    }\n});\nfunction describeListOfPropertyNames(properties) {\n    switch(properties.length){\n        case 0:\n            throw new _invarianterror.InvariantError('Expected describeListOfPropertyNames to be called with a non-empty list of strings.');\n        case 1:\n            return `\\`${properties[0]}\\``;\n        case 2:\n            return `\\`${properties[0]}\\` and \\`${properties[1]}\\``;\n        default:\n            {\n                let description = '';\n                for(let i = 0; i < properties.length - 1; i++){\n                    description += `\\`${properties[i]}\\`, `;\n                }\n                description += `, and \\`${properties[properties.length - 1]}\\``;\n                return description;\n            }\n    }\n}\n\n//# sourceMappingURL=params.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/request/params.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/request/search-params.browser.js":
/*!************************************************************************!*\
  !*** ./node_modules/next/dist/server/request/search-params.browser.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"createRenderSearchParamsFromClient\", ({\n    enumerable: true,\n    get: function() {\n        return createRenderSearchParamsFromClient;\n    }\n}));\nconst _reflect = __webpack_require__(/*! ../web/spec-extension/adapters/reflect */ \"(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/adapters/reflect.js\");\nconst _utils = __webpack_require__(/*! ./utils */ \"(app-pages-browser)/./node_modules/next/dist/server/request/utils.js\");\nfunction createRenderSearchParamsFromClient(underlyingSearchParams) {\n    if (true) {\n        return makeUntrackedExoticSearchParamsWithDevWarnings(underlyingSearchParams);\n    } else {}\n}\nconst CachedSearchParams = new WeakMap();\nfunction makeUntrackedExoticSearchParamsWithDevWarnings(underlyingSearchParams) {\n    const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams);\n    if (cachedSearchParams) {\n        return cachedSearchParams;\n    }\n    const proxiedProperties = new Set();\n    const unproxiedProperties = [];\n    const promise = Promise.resolve(underlyingSearchParams);\n    Object.keys(underlyingSearchParams).forEach((prop)=>{\n        if (_utils.wellKnownProperties.has(prop)) {\n            // These properties cannot be shadowed because they need to be the\n            // true underlying value for Promises to work correctly at runtime\n            unproxiedProperties.push(prop);\n        } else {\n            proxiedProperties.add(prop);\n            promise[prop] = underlyingSearchParams[prop];\n        }\n    });\n    const proxiedPromise = new Proxy(promise, {\n        get (target, prop, receiver) {\n            if (typeof prop === 'string') {\n                if (!_utils.wellKnownProperties.has(prop) && (proxiedProperties.has(prop) || // We are accessing a property that doesn't exist on the promise nor\n                // the underlying searchParams.\n                Reflect.has(target, prop) === false)) {\n                    const expression = (0, _utils.describeStringPropertyAccess)('searchParams', prop);\n                    warnForSyncAccess(expression);\n                }\n            }\n            return _reflect.ReflectAdapter.get(target, prop, receiver);\n        },\n        set (target, prop, value, receiver) {\n            if (typeof prop === 'string') {\n                proxiedProperties.delete(prop);\n            }\n            return Reflect.set(target, prop, value, receiver);\n        },\n        has (target, prop) {\n            if (typeof prop === 'string') {\n                if (!_utils.wellKnownProperties.has(prop) && (proxiedProperties.has(prop) || // We are accessing a property that doesn't exist on the promise nor\n                // the underlying searchParams.\n                Reflect.has(target, prop) === false)) {\n                    const expression = (0, _utils.describeHasCheckingStringProperty)('searchParams', prop);\n                    warnForSyncAccess(expression);\n                }\n            }\n            return Reflect.has(target, prop);\n        },\n        ownKeys (target) {\n            warnForSyncSpread();\n            return Reflect.ownKeys(target);\n        }\n    });\n    CachedSearchParams.set(underlyingSearchParams, proxiedPromise);\n    return proxiedPromise;\n}\nfunction makeUntrackedExoticSearchParams(underlyingSearchParams) {\n    const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams);\n    if (cachedSearchParams) {\n        return cachedSearchParams;\n    }\n    // We don't use makeResolvedReactPromise here because searchParams\n    // supports copying with spread and we don't want to unnecessarily\n    // instrument the promise with spreadable properties of ReactPromise.\n    const promise = Promise.resolve(underlyingSearchParams);\n    CachedSearchParams.set(underlyingSearchParams, promise);\n    Object.keys(underlyingSearchParams).forEach((prop)=>{\n        if (_utils.wellKnownProperties.has(prop)) {\n        // These properties cannot be shadowed because they need to be the\n        // true underlying value for Promises to work correctly at runtime\n        } else {\n            promise[prop] = underlyingSearchParams[prop];\n        }\n    });\n    return promise;\n}\nconst noop = ()=>{};\nconst warnForSyncAccess =  false ? 0 : function warnForSyncAccess(expression) {\n    if (false) {}\n    console.error(`A searchParam property was accessed directly with ${expression}. ` + `\\`searchParams\\` should be unwrapped with \\`React.use()\\` before accessing its properties. ` + `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`);\n};\nconst warnForSyncSpread =  false ? 0 : function warnForSyncSpread() {\n    if (false) {}\n    console.error(`The keys of \\`searchParams\\` were accessed directly. ` + `\\`searchParams\\` should be unwrapped with \\`React.use()\\` before accessing its properties. ` + `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`);\n};\n\n//# sourceMappingURL=search-params.browser.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/request/search-params.browser.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/request/search-params.js":
/*!****************************************************************!*\
  !*** ./node_modules/next/dist/server/request/search-params.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createPrerenderSearchParamsForClientPage: function() {\n        return createPrerenderSearchParamsForClientPage;\n    },\n    createPrerenderSearchParamsFromClient: function() {\n        return createPrerenderSearchParamsFromClient;\n    },\n    createRenderSearchParamsFromClient: function() {\n        return createRenderSearchParamsFromClient;\n    },\n    createServerSearchParamsForMetadata: function() {\n        return createServerSearchParamsForMetadata;\n    },\n    createServerSearchParamsForServerPage: function() {\n        return createServerSearchParamsForServerPage;\n    }\n});\nconst _reflect = __webpack_require__(/*! ../web/spec-extension/adapters/reflect */ \"(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/adapters/reflect.js\");\nconst _dynamicrendering = __webpack_require__(/*! ../app-render/dynamic-rendering */ \"(app-pages-browser)/./node_modules/next/dist/server/app-render/dynamic-rendering.js\");\nconst _workunitasyncstorageexternal = __webpack_require__(/*! ../app-render/work-unit-async-storage.external */ \"(shared)/./node_modules/next/dist/server/app-render/work-unit-async-storage.external.js\");\nconst _invarianterror = __webpack_require__(/*! ../../shared/lib/invariant-error */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/invariant-error.js\");\nconst _dynamicrenderingutils = __webpack_require__(/*! ../dynamic-rendering-utils */ \"(app-pages-browser)/./node_modules/next/dist/server/dynamic-rendering-utils.js\");\nconst _creatededupedbycallsiteservererrorloger = __webpack_require__(/*! ../create-deduped-by-callsite-server-error-loger */ \"(app-pages-browser)/./node_modules/next/dist/server/create-deduped-by-callsite-server-error-loger.js\");\nconst _utils = __webpack_require__(/*! ./utils */ \"(app-pages-browser)/./node_modules/next/dist/server/request/utils.js\");\nfunction createPrerenderSearchParamsFromClient(workStore) {\n    return createPrerenderSearchParams(workStore);\n}\nfunction createRenderSearchParamsFromClient(underlyingSearchParams, workStore) {\n    return createRenderSearchParams(underlyingSearchParams, workStore);\n}\nconst createServerSearchParamsForMetadata = createServerSearchParamsForServerPage;\nfunction createServerSearchParamsForServerPage(underlyingSearchParams, workStore) {\n    if (workStore.isStaticGeneration) {\n        return createPrerenderSearchParams(workStore);\n    } else {\n        return createRenderSearchParams(underlyingSearchParams, workStore);\n    }\n}\nfunction createPrerenderSearchParamsForClientPage(workStore) {\n    if (workStore.forceStatic) {\n        // When using forceStatic we override all other logic and always just return an empty\n        // dictionary object.\n        return Promise.resolve({});\n    }\n    const prerenderStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (prerenderStore && prerenderStore.type === 'prerender') {\n        // dynamicIO Prerender\n        // We're prerendering in a mode that aborts (dynamicIO) and should stall\n        // the promise to ensure the RSC side is considered dynamic\n        return (0, _dynamicrenderingutils.makeHangingPromise)(prerenderStore.renderSignal, '`searchParams`');\n    }\n    // We're prerendering in a mode that does not aborts. We resolve the promise without\n    // any tracking because we're just transporting a value from server to client where the tracking\n    // will be applied.\n    return Promise.resolve({});\n}\nfunction createPrerenderSearchParams(workStore) {\n    if (workStore.forceStatic) {\n        // When using forceStatic we override all other logic and always just return an empty\n        // dictionary object.\n        return Promise.resolve({});\n    }\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (workUnitStore) {\n        if (workUnitStore.type === 'prerender') {\n            // We are in a dynamicIO (PPR or otherwise) prerender\n            return makeAbortingExoticSearchParams(workStore.route, workUnitStore);\n        } else if (workUnitStore.type === 'prerender-legacy' || workUnitStore.type === 'prerender-ppr') {\n            // We are in a legacy static generation and need to interrupt the prerender\n            // when search params are accessed.\n            return makeErroringExoticSearchParams(workStore, workUnitStore);\n        }\n    }\n    throw new _invarianterror.InvariantError('createPrerenderSearchParams called without a prerenderStore in scope. This is a bug in Next.js');\n}\nfunction createRenderSearchParams(underlyingSearchParams, workStore) {\n    if (workStore.forceStatic) {\n        // When using forceStatic we override all other logic and always just return an empty\n        // dictionary object.\n        return Promise.resolve({});\n    } else {\n        if ( true && !workStore.isPrefetchRequest) {\n            return makeDynamicallyTrackedExoticSearchParamsWithDevWarnings(underlyingSearchParams, workStore);\n        } else {\n            return makeUntrackedExoticSearchParams(underlyingSearchParams, workStore);\n        }\n    }\n}\nconst CachedSearchParams = new WeakMap();\nfunction makeAbortingExoticSearchParams(route, prerenderStore) {\n    const cachedSearchParams = CachedSearchParams.get(prerenderStore);\n    if (cachedSearchParams) {\n        return cachedSearchParams;\n    }\n    const promise = (0, _dynamicrenderingutils.makeHangingPromise)(prerenderStore.renderSignal, '`searchParams`');\n    const proxiedPromise = new Proxy(promise, {\n        get (target, prop, receiver) {\n            if (Object.hasOwn(promise, prop)) {\n                // The promise has this property directly. we must return it.\n                // We know it isn't a dynamic access because it can only be something\n                // that was previously written to the promise and thus not an underlying searchParam value\n                return _reflect.ReflectAdapter.get(target, prop, receiver);\n            }\n            switch(prop){\n                case 'then':\n                    {\n                        const expression = '`await searchParams`, `searchParams.then`, or similar';\n                        (0, _dynamicrendering.annotateDynamicAccess)(expression, prerenderStore);\n                        return _reflect.ReflectAdapter.get(target, prop, receiver);\n                    }\n                case 'status':\n                    {\n                        const expression = '`use(searchParams)`, `searchParams.status`, or similar';\n                        (0, _dynamicrendering.annotateDynamicAccess)(expression, prerenderStore);\n                        return _reflect.ReflectAdapter.get(target, prop, receiver);\n                    }\n                // Object prototype\n                case 'hasOwnProperty':\n                case 'isPrototypeOf':\n                case 'propertyIsEnumerable':\n                case 'toString':\n                case 'valueOf':\n                case 'toLocaleString':\n                // Promise prototype\n                // fallthrough\n                case 'catch':\n                case 'finally':\n                // Common tested properties\n                // fallthrough\n                case 'toJSON':\n                case '$$typeof':\n                case '__esModule':\n                    {\n                        // These properties cannot be shadowed because they need to be the\n                        // true underlying value for Promises to work correctly at runtime\n                        return _reflect.ReflectAdapter.get(target, prop, receiver);\n                    }\n                default:\n                    {\n                        if (typeof prop === 'string') {\n                            const expression = (0, _utils.describeStringPropertyAccess)('searchParams', prop);\n                            const error = createSyncSearchParamsError(route, expression);\n                            (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n                        }\n                        return _reflect.ReflectAdapter.get(target, prop, receiver);\n                    }\n            }\n        },\n        has (target, prop) {\n            // We don't expect key checking to be used except for testing the existence of\n            // searchParams so we make all has tests trigger dynamic. this means that `promise.then`\n            // can resolve to the then function on the Promise prototype but 'then' in promise will assume\n            // you are testing whether the searchParams has a 'then' property.\n            if (typeof prop === 'string') {\n                const expression = (0, _utils.describeHasCheckingStringProperty)('searchParams', prop);\n                const error = createSyncSearchParamsError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n            return _reflect.ReflectAdapter.has(target, prop);\n        },\n        ownKeys () {\n            const expression = '`{...searchParams}`, `Object.keys(searchParams)`, or similar';\n            const error = createSyncSearchParamsError(route, expression);\n            (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n        }\n    });\n    CachedSearchParams.set(prerenderStore, proxiedPromise);\n    return proxiedPromise;\n}\nfunction makeErroringExoticSearchParams(workStore, prerenderStore) {\n    const cachedSearchParams = CachedSearchParams.get(workStore);\n    if (cachedSearchParams) {\n        return cachedSearchParams;\n    }\n    const underlyingSearchParams = {};\n    // For search params we don't construct a ReactPromise because we want to interrupt\n    // rendering on any property access that was not set from outside and so we only want\n    // to have properties like value and status if React sets them.\n    const promise = Promise.resolve(underlyingSearchParams);\n    const proxiedPromise = new Proxy(promise, {\n        get (target, prop, receiver) {\n            if (Object.hasOwn(promise, prop)) {\n                // The promise has this property directly. we must return it.\n                // We know it isn't a dynamic access because it can only be something\n                // that was previously written to the promise and thus not an underlying searchParam value\n                return _reflect.ReflectAdapter.get(target, prop, receiver);\n            }\n            switch(prop){\n                // Object prototype\n                case 'hasOwnProperty':\n                case 'isPrototypeOf':\n                case 'propertyIsEnumerable':\n                case 'toString':\n                case 'valueOf':\n                case 'toLocaleString':\n                // Promise prototype\n                // fallthrough\n                case 'catch':\n                case 'finally':\n                // Common tested properties\n                // fallthrough\n                case 'toJSON':\n                case '$$typeof':\n                case '__esModule':\n                    {\n                        // These properties cannot be shadowed because they need to be the\n                        // true underlying value for Promises to work correctly at runtime\n                        return _reflect.ReflectAdapter.get(target, prop, receiver);\n                    }\n                case 'then':\n                    {\n                        const expression = '`await searchParams`, `searchParams.then`, or similar';\n                        if (workStore.dynamicShouldError) {\n                            (0, _utils.throwWithStaticGenerationBailoutErrorWithDynamicError)(workStore.route, expression);\n                        } else if (prerenderStore.type === 'prerender-ppr') {\n                            // PPR Prerender (no dynamicIO)\n                            (0, _dynamicrendering.postponeWithTracking)(workStore.route, expression, prerenderStore.dynamicTracking);\n                        } else {\n                            // Legacy Prerender\n                            (0, _dynamicrendering.throwToInterruptStaticGeneration)(expression, workStore, prerenderStore);\n                        }\n                        return;\n                    }\n                case 'status':\n                    {\n                        const expression = '`use(searchParams)`, `searchParams.status`, or similar';\n                        if (workStore.dynamicShouldError) {\n                            (0, _utils.throwWithStaticGenerationBailoutErrorWithDynamicError)(workStore.route, expression);\n                        } else if (prerenderStore.type === 'prerender-ppr') {\n                            // PPR Prerender (no dynamicIO)\n                            (0, _dynamicrendering.postponeWithTracking)(workStore.route, expression, prerenderStore.dynamicTracking);\n                        } else {\n                            // Legacy Prerender\n                            (0, _dynamicrendering.throwToInterruptStaticGeneration)(expression, workStore, prerenderStore);\n                        }\n                        return;\n                    }\n                default:\n                    {\n                        if (typeof prop === 'string') {\n                            const expression = (0, _utils.describeStringPropertyAccess)('searchParams', prop);\n                            if (workStore.dynamicShouldError) {\n                                (0, _utils.throwWithStaticGenerationBailoutErrorWithDynamicError)(workStore.route, expression);\n                            } else if (prerenderStore.type === 'prerender-ppr') {\n                                // PPR Prerender (no dynamicIO)\n                                (0, _dynamicrendering.postponeWithTracking)(workStore.route, expression, prerenderStore.dynamicTracking);\n                            } else {\n                                // Legacy Prerender\n                                (0, _dynamicrendering.throwToInterruptStaticGeneration)(expression, workStore, prerenderStore);\n                            }\n                        }\n                        return _reflect.ReflectAdapter.get(target, prop, receiver);\n                    }\n            }\n        },\n        has (target, prop) {\n            // We don't expect key checking to be used except for testing the existence of\n            // searchParams so we make all has tests trigger dynamic. this means that `promise.then`\n            // can resolve to the then function on the Promise prototype but 'then' in promise will assume\n            // you are testing whether the searchParams has a 'then' property.\n            if (typeof prop === 'string') {\n                const expression = (0, _utils.describeHasCheckingStringProperty)('searchParams', prop);\n                if (workStore.dynamicShouldError) {\n                    (0, _utils.throwWithStaticGenerationBailoutErrorWithDynamicError)(workStore.route, expression);\n                } else if (prerenderStore.type === 'prerender-ppr') {\n                    // PPR Prerender (no dynamicIO)\n                    (0, _dynamicrendering.postponeWithTracking)(workStore.route, expression, prerenderStore.dynamicTracking);\n                } else {\n                    // Legacy Prerender\n                    (0, _dynamicrendering.throwToInterruptStaticGeneration)(expression, workStore, prerenderStore);\n                }\n                return false;\n            }\n            return _reflect.ReflectAdapter.has(target, prop);\n        },\n        ownKeys () {\n            const expression = '`{...searchParams}`, `Object.keys(searchParams)`, or similar';\n            if (workStore.dynamicShouldError) {\n                (0, _utils.throwWithStaticGenerationBailoutErrorWithDynamicError)(workStore.route, expression);\n            } else if (prerenderStore.type === 'prerender-ppr') {\n                // PPR Prerender (no dynamicIO)\n                (0, _dynamicrendering.postponeWithTracking)(workStore.route, expression, prerenderStore.dynamicTracking);\n            } else {\n                // Legacy Prerender\n                (0, _dynamicrendering.throwToInterruptStaticGeneration)(expression, workStore, prerenderStore);\n            }\n        }\n    });\n    CachedSearchParams.set(workStore, proxiedPromise);\n    return proxiedPromise;\n}\nfunction makeUntrackedExoticSearchParams(underlyingSearchParams, store) {\n    const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams);\n    if (cachedSearchParams) {\n        return cachedSearchParams;\n    }\n    // We don't use makeResolvedReactPromise here because searchParams\n    // supports copying with spread and we don't want to unnecessarily\n    // instrument the promise with spreadable properties of ReactPromise.\n    const promise = Promise.resolve(underlyingSearchParams);\n    CachedSearchParams.set(underlyingSearchParams, promise);\n    Object.keys(underlyingSearchParams).forEach((prop)=>{\n        switch(prop){\n            // Object prototype\n            case 'hasOwnProperty':\n            case 'isPrototypeOf':\n            case 'propertyIsEnumerable':\n            case 'toString':\n            case 'valueOf':\n            case 'toLocaleString':\n            // Promise prototype\n            // fallthrough\n            case 'then':\n            case 'catch':\n            case 'finally':\n            // React Promise extension\n            // fallthrough\n            case 'status':\n            // Common tested properties\n            // fallthrough\n            case 'toJSON':\n            case '$$typeof':\n            case '__esModule':\n                {\n                    break;\n                }\n            default:\n                {\n                    Object.defineProperty(promise, prop, {\n                        get () {\n                            const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n                            (0, _dynamicrendering.trackDynamicDataInDynamicRender)(store, workUnitStore);\n                            return underlyingSearchParams[prop];\n                        },\n                        set (value) {\n                            Object.defineProperty(promise, prop, {\n                                value,\n                                writable: true,\n                                enumerable: true\n                            });\n                        },\n                        enumerable: true,\n                        configurable: true\n                    });\n                }\n        }\n    });\n    return promise;\n}\nfunction makeDynamicallyTrackedExoticSearchParamsWithDevWarnings(underlyingSearchParams, store) {\n    const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams);\n    if (cachedSearchParams) {\n        return cachedSearchParams;\n    }\n    const proxiedProperties = new Set();\n    const unproxiedProperties = [];\n    // We have an unfortunate sequence of events that requires this initialization logic. We want to instrument the underlying\n    // searchParams object to detect if you are accessing values in dev. This is used for warnings and for things like the static prerender\n    // indicator. However when we pass this proxy to our Promise.resolve() below the VM checks if the resolved value is a promise by looking\n    // at the `.then` property. To our dynamic tracking logic this is indistinguishable from a `then` searchParam and so we would normally trigger\n    // dynamic tracking. However we know that this .then is not real dynamic access, it's just how thenables resolve in sequence. So we introduce\n    // this initialization concept so we omit the dynamic check until after we've constructed our resolved promise.\n    let promiseInitialized = false;\n    const proxiedUnderlying = new Proxy(underlyingSearchParams, {\n        get (target, prop, receiver) {\n            if (typeof prop === 'string' && promiseInitialized) {\n                if (store.dynamicShouldError) {\n                    const expression = (0, _utils.describeStringPropertyAccess)('searchParams', prop);\n                    (0, _utils.throwWithStaticGenerationBailoutErrorWithDynamicError)(store.route, expression);\n                }\n                const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n                (0, _dynamicrendering.trackDynamicDataInDynamicRender)(store, workUnitStore);\n            }\n            return _reflect.ReflectAdapter.get(target, prop, receiver);\n        },\n        has (target, prop) {\n            if (typeof prop === 'string') {\n                if (store.dynamicShouldError) {\n                    const expression = (0, _utils.describeHasCheckingStringProperty)('searchParams', prop);\n                    (0, _utils.throwWithStaticGenerationBailoutErrorWithDynamicError)(store.route, expression);\n                }\n            }\n            return Reflect.has(target, prop);\n        },\n        ownKeys (target) {\n            if (store.dynamicShouldError) {\n                const expression = '`{...searchParams}`, `Object.keys(searchParams)`, or similar';\n                (0, _utils.throwWithStaticGenerationBailoutErrorWithDynamicError)(store.route, expression);\n            }\n            return Reflect.ownKeys(target);\n        }\n    });\n    // We don't use makeResolvedReactPromise here because searchParams\n    // supports copying with spread and we don't want to unnecessarily\n    // instrument the promise with spreadable properties of ReactPromise.\n    const promise = Promise.resolve(proxiedUnderlying);\n    promise.then(()=>{\n        promiseInitialized = true;\n    });\n    Object.keys(underlyingSearchParams).forEach((prop)=>{\n        if (_utils.wellKnownProperties.has(prop)) {\n            // These properties cannot be shadowed because they need to be the\n            // true underlying value for Promises to work correctly at runtime\n            unproxiedProperties.push(prop);\n        } else {\n            proxiedProperties.add(prop);\n            Object.defineProperty(promise, prop, {\n                get () {\n                    return proxiedUnderlying[prop];\n                },\n                set (newValue) {\n                    Object.defineProperty(promise, prop, {\n                        value: newValue,\n                        writable: true,\n                        enumerable: true\n                    });\n                },\n                enumerable: true,\n                configurable: true\n            });\n        }\n    });\n    const proxiedPromise = new Proxy(promise, {\n        get (target, prop, receiver) {\n            if (typeof prop === 'string') {\n                if (!_utils.wellKnownProperties.has(prop) && (proxiedProperties.has(prop) || // We are accessing a property that doesn't exist on the promise nor\n                // the underlying searchParams.\n                Reflect.has(target, prop) === false)) {\n                    const expression = (0, _utils.describeStringPropertyAccess)('searchParams', prop);\n                    warnForSyncAccess(store.route, expression);\n                }\n            }\n            return _reflect.ReflectAdapter.get(target, prop, receiver);\n        },\n        set (target, prop, value, receiver) {\n            if (typeof prop === 'string') {\n                proxiedProperties.delete(prop);\n            }\n            return Reflect.set(target, prop, value, receiver);\n        },\n        has (target, prop) {\n            if (typeof prop === 'string') {\n                if (!_utils.wellKnownProperties.has(prop) && (proxiedProperties.has(prop) || // We are accessing a property that doesn't exist on the promise nor\n                // the underlying searchParams.\n                Reflect.has(target, prop) === false)) {\n                    const expression = (0, _utils.describeHasCheckingStringProperty)('searchParams', prop);\n                    warnForSyncAccess(store.route, expression);\n                }\n            }\n            return Reflect.has(target, prop);\n        },\n        ownKeys (target) {\n            warnForEnumeration(store.route, unproxiedProperties);\n            return Reflect.ownKeys(target);\n        }\n    });\n    CachedSearchParams.set(underlyingSearchParams, proxiedPromise);\n    return proxiedPromise;\n}\nconst noop = ()=>{};\nconst warnForSyncAccess =  false ? 0 : (0, _creatededupedbycallsiteservererrorloger.createDedupedByCallsiteServerErrorLoggerDev)(function getSyncAccessMessage(route, expression) {\n    const prefix = route ? ` In route ${route} a ` : 'A ';\n    return new Error(`${prefix}searchParam property was accessed directly with ${expression}. ` + `\\`searchParams\\` should be awaited before accessing properties. ` + `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`);\n});\nconst warnForEnumeration =  false ? 0 : (0, _creatededupedbycallsiteservererrorloger.createDedupedByCallsiteServerErrorLoggerDev)(function getEnumerationMessage(route, missingProperties) {\n    const prefix = route ? ` In route ${route} ` : '';\n    if (missingProperties.length) {\n        const describedMissingProperties = describeListOfPropertyNames(missingProperties);\n        return new Error(`${prefix}searchParams are being enumerated incompletely missing these properties: ${describedMissingProperties}. ` + `\\`searchParams\\` should be awaited before accessing its properties. ` + `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`);\n    } else {\n        return new Error(`${prefix}searchParams are being enumerated. ` + `\\`searchParams\\` should be awaited before accessing its properties. ` + `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`);\n    }\n});\nfunction describeListOfPropertyNames(properties) {\n    switch(properties.length){\n        case 0:\n            throw new _invarianterror.InvariantError('Expected describeListOfPropertyNames to be called with a non-empty list of strings.');\n        case 1:\n            return `\\`${properties[0]}\\``;\n        case 2:\n            return `\\`${properties[0]}\\` and \\`${properties[1]}\\``;\n        default:\n            {\n                let description = '';\n                for(let i = 0; i < properties.length - 1; i++){\n                    description += `\\`${properties[i]}\\`, `;\n                }\n                description += `, and \\`${properties[properties.length - 1]}\\``;\n                return description;\n            }\n    }\n}\nfunction createSyncSearchParamsError(route, expression) {\n    return new Error(`Route \"${route}\" used ${expression}. \\`searchParams\\` is now a Promise and should be \\`awaited\\` before accessing search param values. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-params`);\n}\n\n//# sourceMappingURL=search-params.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/request/search-params.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/request/utils.js":
/*!********************************************************!*\
  !*** ./node_modules/next/dist/server/request/utils.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    describeHasCheckingStringProperty: function() {\n        return describeHasCheckingStringProperty;\n    },\n    describeStringPropertyAccess: function() {\n        return describeStringPropertyAccess;\n    },\n    makeResolvedReactPromise: function() {\n        return makeResolvedReactPromise;\n    },\n    throwWithStaticGenerationBailoutError: function() {\n        return throwWithStaticGenerationBailoutError;\n    },\n    throwWithStaticGenerationBailoutErrorWithDynamicError: function() {\n        return throwWithStaticGenerationBailoutErrorWithDynamicError;\n    },\n    wellKnownProperties: function() {\n        return wellKnownProperties;\n    }\n});\nconst _staticgenerationbailout = __webpack_require__(/*! ../../client/components/static-generation-bailout */ \"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-bailout.js\");\nfunction makeResolvedReactPromise(value) {\n    const promise = Promise.resolve(value);\n    promise.status = 'fulfilled';\n    promise.value = value;\n    return promise;\n}\n// This regex will have fast negatives meaning valid identifiers may not pass\n// this test. However this is only used during static generation to provide hints\n// about why a page bailed out of some or all prerendering and we can use bracket notation\n// for example while `ಠ_ಠ` is a valid identifier it's ok to print `searchParams['ಠ_ಠ']`\n// even if this would have been fine too `searchParams.ಠ_ಠ`\nconst isDefinitelyAValidIdentifier = /^[A-Za-z_$][A-Za-z0-9_$]*$/;\nfunction describeStringPropertyAccess(target, prop) {\n    if (isDefinitelyAValidIdentifier.test(prop)) {\n        return `\\`${target}.${prop}\\``;\n    }\n    return `\\`${target}[${JSON.stringify(prop)}]\\``;\n}\nfunction describeHasCheckingStringProperty(target, prop) {\n    const stringifiedProp = JSON.stringify(prop);\n    return `\\`Reflect.has(${target}, ${stringifiedProp})\\`, \\`${stringifiedProp} in ${target}\\`, or similar`;\n}\nfunction throwWithStaticGenerationBailoutError(route, expression) {\n    throw new _staticgenerationbailout.StaticGenBailoutError(`Route ${route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`);\n}\nfunction throwWithStaticGenerationBailoutErrorWithDynamicError(route, expression) {\n    throw new _staticgenerationbailout.StaticGenBailoutError(`Route ${route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`);\n}\nconst wellKnownProperties = new Set([\n    'hasOwnProperty',\n    'isPrototypeOf',\n    'propertyIsEnumerable',\n    'toString',\n    'valueOf',\n    'toLocaleString',\n    // Promise prototype\n    // fallthrough\n    'then',\n    'catch',\n    'finally',\n    // React Promise extension\n    // fallthrough\n    'status',\n    // Common tested properties\n    // fallthrough\n    'toJSON',\n    '$$typeof',\n    '__esModule'\n]);\n\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/request/utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/adapters/reflect.js":
/*!******************************************************************************!*\
  !*** ./node_modules/next/dist/server/web/spec-extension/adapters/reflect.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"ReflectAdapter\", ({\n    enumerable: true,\n    get: function() {\n        return ReflectAdapter;\n    }\n}));\nclass ReflectAdapter {\n    static get(target, prop, receiver) {\n        const value = Reflect.get(target, prop, receiver);\n        if (typeof value === 'function') {\n            return value.bind(target);\n        }\n        return value;\n    }\n    static set(target, prop, value, receiver) {\n        return Reflect.set(target, prop, value, receiver);\n    }\n    static has(target, prop) {\n        return Reflect.has(target, prop);\n    }\n    static deleteProperty(target, prop) {\n        return Reflect.deleteProperty(target, prop);\n    }\n}\n\n//# sourceMappingURL=reflect.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2VydmVyL3dlYi9zcGVjLWV4dGVuc2lvbi9hZGFwdGVycy9yZWZsZWN0LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDO0FBQzdDO0FBQ0EsQ0FBQyxFQUFDO0FBQ0Ysa0RBQWlEO0FBQ2pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxFQUFDO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2VydmVyL3dlYi9zcGVjLWV4dGVuc2lvbi9hZGFwdGVycy9yZWZsZWN0LmpzP2I3MzgiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgICB2YWx1ZTogdHJ1ZVxufSk7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJSZWZsZWN0QWRhcHRlclwiLCB7XG4gICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICBnZXQ6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gUmVmbGVjdEFkYXB0ZXI7XG4gICAgfVxufSk7XG5jbGFzcyBSZWZsZWN0QWRhcHRlciB7XG4gICAgc3RhdGljIGdldCh0YXJnZXQsIHByb3AsIHJlY2VpdmVyKSB7XG4gICAgICAgIGNvbnN0IHZhbHVlID0gUmVmbGVjdC5nZXQodGFyZ2V0LCBwcm9wLCByZWNlaXZlcik7XG4gICAgICAgIGlmICh0eXBlb2YgdmFsdWUgPT09ICdmdW5jdGlvbicpIHtcbiAgICAgICAgICAgIHJldHVybiB2YWx1ZS5iaW5kKHRhcmdldCk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHZhbHVlO1xuICAgIH1cbiAgICBzdGF0aWMgc2V0KHRhcmdldCwgcHJvcCwgdmFsdWUsIHJlY2VpdmVyKSB7XG4gICAgICAgIHJldHVybiBSZWZsZWN0LnNldCh0YXJnZXQsIHByb3AsIHZhbHVlLCByZWNlaXZlcik7XG4gICAgfVxuICAgIHN0YXRpYyBoYXModGFyZ2V0LCBwcm9wKSB7XG4gICAgICAgIHJldHVybiBSZWZsZWN0Lmhhcyh0YXJnZXQsIHByb3ApO1xuICAgIH1cbiAgICBzdGF0aWMgZGVsZXRlUHJvcGVydHkodGFyZ2V0LCBwcm9wKSB7XG4gICAgICAgIHJldHVybiBSZWZsZWN0LmRlbGV0ZVByb3BlcnR5KHRhcmdldCwgcHJvcCk7XG4gICAgfVxufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1yZWZsZWN0LmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/adapters/reflect.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":
/*!*****************************************************************!*\
  !*** ./node_modules/next/dist/client/components/client-page.js ***!
  \*****************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"ClientPageRoot\", ({\n    enumerable: true,\n    get: function() {\n        return ClientPageRoot;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _invarianterror = __webpack_require__(/*! ../../shared/lib/invariant-error */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/invariant-error.js\");\nfunction ClientPageRoot(param) {\n    let { Component, searchParams, params, promises } = param;\n    if (typeof window === 'undefined') {\n        const { workAsyncStorage } = __webpack_require__(/*! ../../server/app-render/work-async-storage.external */ \"(shared)/./node_modules/next/dist/server/app-render/work-async-storage.external.js\");\n        let clientSearchParams;\n        let clientParams;\n        // We are going to instrument the searchParams prop with tracking for the\n        // appropriate context. We wrap differently in prerendering vs rendering\n        const store = workAsyncStorage.getStore();\n        if (!store) {\n            throw new _invarianterror.InvariantError('Expected workStore to exist when handling searchParams in a client Page.');\n        }\n        if (store.isStaticGeneration) {\n            // We are in a prerender context\n            const { createPrerenderSearchParamsFromClient } = __webpack_require__(/*! ../../server/request/search-params */ \"(app-pages-browser)/./node_modules/next/dist/server/request/search-params.js\");\n            clientSearchParams = createPrerenderSearchParamsFromClient(store);\n            const { createPrerenderParamsFromClient } = __webpack_require__(/*! ../../server/request/params */ \"(app-pages-browser)/./node_modules/next/dist/server/request/params.js\");\n            clientParams = createPrerenderParamsFromClient(params, store);\n        } else {\n            const { createRenderSearchParamsFromClient } = __webpack_require__(/*! ../../server/request/search-params */ \"(app-pages-browser)/./node_modules/next/dist/server/request/search-params.js\");\n            clientSearchParams = createRenderSearchParamsFromClient(searchParams, store);\n            const { createRenderParamsFromClient } = __webpack_require__(/*! ../../server/request/params */ \"(app-pages-browser)/./node_modules/next/dist/server/request/params.js\");\n            clientParams = createRenderParamsFromClient(params, store);\n        }\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(Component, {\n            params: clientParams,\n            searchParams: clientSearchParams\n        });\n    } else {\n        const { createRenderSearchParamsFromClient } = __webpack_require__(/*! ../../server/request/search-params.browser */ \"(app-pages-browser)/./node_modules/next/dist/server/request/search-params.browser.js\");\n        const clientSearchParams = createRenderSearchParamsFromClient(searchParams);\n        const { createRenderParamsFromClient } = __webpack_require__(/*! ../../server/request/params.browser */ \"(app-pages-browser)/./node_modules/next/dist/server/request/params.browser.js\");\n        const clientParams = createRenderParamsFromClient(params);\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(Component, {\n            params: clientParams,\n            searchParams: clientSearchParams\n        });\n    }\n}\n_c = ClientPageRoot;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=client-page.js.map\nvar _c;\n$RefreshReg$(_c, \"ClientPageRoot\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvY2xpZW50LXBhZ2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O2VBZWdCQTs7Ozs2SUFaZTtBQVl4QixTQUFTQSxlQUFlLG1CQVc5QjtJQVg4QixvQkFFN0JFLEtBQUFBO0lBR0FFLElBQUFBLEVBQUFBLEVBQVEsRUFNVCxHQVg4QiwwQkFZN0IsSUFBSSxPQUFPQyxFQUFBQTtRQUNULE1BQU0sRUFBRUMsVUFBQUEsTUFBZ0IsRUFBRSxHQUN4QkMsRUFBQUE7UUFFRixJQUFJQyxFQUFBQSxFQUFBQSxnQkFBQUEsRUFBQUEsR0FBQUEsbUJBQUFBLENBQUFBLCtJQUFBQTtRQUNKLElBQUlDO1FBQ0o7UUFDQSx3RUFBd0U7UUFDeEUsTUFBTUMsUUFBUUosaUJBQWlCSyxRQUFRO1FBQ3ZDLElBQUksQ0FBQ0QsQ0FBQUEsTUFBTztZQUNWLE1BQU07WUFHUjtRQUVBO1lBQ0U7WUFDQSxNQUFNLEVBQUVJLHdCQUFBQTtZQUVSTixNQUFBQSxFQUFBQSxhQUFxQk0sd0JBQUFBLEVBQUFBLEdBQUFBLG1CQUFBQSxDQUFBQSx3SEFBc0NKO1lBRTNELE1BQU0sRUFBRUssYUFBQUEsa0JBQStCLEVBQUUsR0FDdkNSLFFBQVE7WUFFVkUsTUFBQUEsRUFBQUEsT0FBZU0sd0JBQUFBLEVBQUFBLEdBQUFBLG1CQUFnQ1osQ0FBQUEsMEdBQVFPO1lBQ3pELEdBQU87WUFDTDtZQUVBRixNQUFBQSxFQUFBQSxhQUFxQlEscUJBQUFBLEVBQUFBLEdBQUFBLG1CQUFBQSxDQUFBQSx3SEFFbkJOO1lBRUYsTUFBTSxFQUFFTyxhQUFBQSxlQUE0QixFQUFFLEdBQ3BDVixRQUFRO1lBQ1ZFLE1BQUFBLEVBQUFBLE9BQWVRLHFCQUFBQSxFQUFBQSxHQUFBQSxtQkFBNkJkLENBQUFBLDBHQUFRTztZQUN0RDtRQUVBO1lBQWtCUCxHQUFBQSxLQUFRTSxNQUFBQSxHQUFBQSxDQUFBQSxHQUFBQSxZQUFBQSxHQUFBQSxFQUFBQSxXQUFBQTtZQUFjUCxRQUFBQSxNQUFjTTs7UUFDeEQsR0FBTztRQUNMO1FBRUEsTUFBTUEsRUFBQUEsbUJBQXFCUSxlQUFBQSxFQUFBQSxHQUFBQSxtQkFBQUEsQ0FBQUEsd0lBQW1DZDtRQUM5RCxNQUFNLEVBQUVlLG1CQUFBQSxTQUE0QixFQUFFLEdBQ3BDVixRQUFRO1FBQ1YsTUFBTUUsRUFBQUEsYUFBZVEsZUFBQUEsRUFBQUEsR0FBQUEsbUJBQUFBLENBQUFBLDBIQUE2QmQ7UUFFbEQscUJBQU8scUJBQUNGLFFBQUFBO1lBQVVFLEdBQUFBLEtBQVFNLE1BQUFBLEdBQUFBLENBQUFBLEdBQUFBLFlBQUFBLEdBQUFBLEVBQUFBLFdBQUFBO1lBQWNQLFFBQUFBLE1BQWNNOztRQUN4RDtJQUNGOztLQTVEK0IsQ0FDN0JQLFNBQVMsRUFDVEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uLy4uL3NyYy9jbGllbnQvY29tcG9uZW50cy9jbGllbnQtcGFnZS50c3g/ODRjNSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHR5cGUgeyBQYXJzZWRVcmxRdWVyeSB9IGZyb20gJ3F1ZXJ5c3RyaW5nJ1xuaW1wb3J0IHsgSW52YXJpYW50RXJyb3IgfSBmcm9tICcuLi8uLi9zaGFyZWQvbGliL2ludmFyaWFudC1lcnJvcidcblxuaW1wb3J0IHR5cGUgeyBQYXJhbXMgfSBmcm9tICcuLi8uLi9zZXJ2ZXIvcmVxdWVzdC9wYXJhbXMnXG5cbi8qKlxuICogV2hlbiB0aGUgUGFnZSBpcyBhIGNsaWVudCBjb21wb25lbnQgd2Ugc2VuZCB0aGUgcGFyYW1zIGFuZCBzZWFyY2hQYXJhbXMgdG8gdGhpcyBjbGllbnQgd3JhcHBlclxuICogd2hlcmUgdGhleSBhcmUgdHVybmVkIGludG8gZHluYW1pY2FsbHkgdHJhY2tlZCB2YWx1ZXMgYmVmb3JlIGJlaW5nIHBhc3NlZCB0byB0aGUgYWN0dWFsIFBhZ2UgY29tcG9uZW50LlxuICpcbiAqIGFkZGl0aW9uYWxseSB3ZSBtYXkgc2VuZCBwcm9taXNlcyByZXByZXNlbnRpbmcgdGhlIHBhcmFtcyBhbmQgc2VhcmNoUGFyYW1zLiBXZSBkb24ndCBldmVyIHVzZSB0aGVzZSBwYXNzZWRcbiAqIHZhbHVlcyBidXQgaXQgY2FuIGJlIG5lY2Vzc2FyeSBmb3IgdGhlIHNlbmRlciB0byBzZW5kIGEgUHJvbWlzZSB0aGF0IGRvZXNuJ3QgcmVzb2x2ZSBpbiBjZXJ0YWluIHNpdHVhdGlvbnMuXG4gKiBJdCBpcyB1cCB0byB0aGUgY2FsbGVyIHRvIGRlY2lkZSBpZiB0aGUgcHJvbWlzZXMgYXJlIG5lZWRlZC5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIENsaWVudFBhZ2VSb290KHtcbiAgQ29tcG9uZW50LFxuICBzZWFyY2hQYXJhbXMsXG4gIHBhcmFtcyxcbiAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIEB0eXBlc2NyaXB0LWVzbGludC9uby11bnVzZWQtdmFyc1xuICBwcm9taXNlcyxcbn06IHtcbiAgQ29tcG9uZW50OiBSZWFjdC5Db21wb25lbnRUeXBlPGFueT5cbiAgc2VhcmNoUGFyYW1zOiBQYXJzZWRVcmxRdWVyeVxuICBwYXJhbXM6IFBhcmFtc1xuICBwcm9taXNlcz86IEFycmF5PFByb21pc2U8YW55Pj5cbn0pIHtcbiAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgY29uc3QgeyB3b3JrQXN5bmNTdG9yYWdlIH0gPVxuICAgICAgcmVxdWlyZSgnLi4vLi4vc2VydmVyL2FwcC1yZW5kZXIvd29yay1hc3luYy1zdG9yYWdlLmV4dGVybmFsJykgYXMgdHlwZW9mIGltcG9ydCgnLi4vLi4vc2VydmVyL2FwcC1yZW5kZXIvd29yay1hc3luYy1zdG9yYWdlLmV4dGVybmFsJylcblxuICAgIGxldCBjbGllbnRTZWFyY2hQYXJhbXM6IFByb21pc2U8UGFyc2VkVXJsUXVlcnk+XG4gICAgbGV0IGNsaWVudFBhcmFtczogUHJvbWlzZTxQYXJhbXM+XG4gICAgLy8gV2UgYXJlIGdvaW5nIHRvIGluc3RydW1lbnQgdGhlIHNlYXJjaFBhcmFtcyBwcm9wIHdpdGggdHJhY2tpbmcgZm9yIHRoZVxuICAgIC8vIGFwcHJvcHJpYXRlIGNvbnRleHQuIFdlIHdyYXAgZGlmZmVyZW50bHkgaW4gcHJlcmVuZGVyaW5nIHZzIHJlbmRlcmluZ1xuICAgIGNvbnN0IHN0b3JlID0gd29ya0FzeW5jU3RvcmFnZS5nZXRTdG9yZSgpXG4gICAgaWYgKCFzdG9yZSkge1xuICAgICAgdGhyb3cgbmV3IEludmFyaWFudEVycm9yKFxuICAgICAgICAnRXhwZWN0ZWQgd29ya1N0b3JlIHRvIGV4aXN0IHdoZW4gaGFuZGxpbmcgc2VhcmNoUGFyYW1zIGluIGEgY2xpZW50IFBhZ2UuJ1xuICAgICAgKVxuICAgIH1cblxuICAgIGlmIChzdG9yZS5pc1N0YXRpY0dlbmVyYXRpb24pIHtcbiAgICAgIC8vIFdlIGFyZSBpbiBhIHByZXJlbmRlciBjb250ZXh0XG4gICAgICBjb25zdCB7IGNyZWF0ZVByZXJlbmRlclNlYXJjaFBhcmFtc0Zyb21DbGllbnQgfSA9XG4gICAgICAgIHJlcXVpcmUoJy4uLy4uL3NlcnZlci9yZXF1ZXN0L3NlYXJjaC1wYXJhbXMnKSBhcyB0eXBlb2YgaW1wb3J0KCcuLi8uLi9zZXJ2ZXIvcmVxdWVzdC9zZWFyY2gtcGFyYW1zJylcbiAgICAgIGNsaWVudFNlYXJjaFBhcmFtcyA9IGNyZWF0ZVByZXJlbmRlclNlYXJjaFBhcmFtc0Zyb21DbGllbnQoc3RvcmUpXG5cbiAgICAgIGNvbnN0IHsgY3JlYXRlUHJlcmVuZGVyUGFyYW1zRnJvbUNsaWVudCB9ID1cbiAgICAgICAgcmVxdWlyZSgnLi4vLi4vc2VydmVyL3JlcXVlc3QvcGFyYW1zJykgYXMgdHlwZW9mIGltcG9ydCgnLi4vLi4vc2VydmVyL3JlcXVlc3QvcGFyYW1zJylcblxuICAgICAgY2xpZW50UGFyYW1zID0gY3JlYXRlUHJlcmVuZGVyUGFyYW1zRnJvbUNsaWVudChwYXJhbXMsIHN0b3JlKVxuICAgIH0gZWxzZSB7XG4gICAgICBjb25zdCB7IGNyZWF0ZVJlbmRlclNlYXJjaFBhcmFtc0Zyb21DbGllbnQgfSA9XG4gICAgICAgIHJlcXVpcmUoJy4uLy4uL3NlcnZlci9yZXF1ZXN0L3NlYXJjaC1wYXJhbXMnKSBhcyB0eXBlb2YgaW1wb3J0KCcuLi8uLi9zZXJ2ZXIvcmVxdWVzdC9zZWFyY2gtcGFyYW1zJylcbiAgICAgIGNsaWVudFNlYXJjaFBhcmFtcyA9IGNyZWF0ZVJlbmRlclNlYXJjaFBhcmFtc0Zyb21DbGllbnQoXG4gICAgICAgIHNlYXJjaFBhcmFtcyxcbiAgICAgICAgc3RvcmVcbiAgICAgIClcbiAgICAgIGNvbnN0IHsgY3JlYXRlUmVuZGVyUGFyYW1zRnJvbUNsaWVudCB9ID1cbiAgICAgICAgcmVxdWlyZSgnLi4vLi4vc2VydmVyL3JlcXVlc3QvcGFyYW1zJykgYXMgdHlwZW9mIGltcG9ydCgnLi4vLi4vc2VydmVyL3JlcXVlc3QvcGFyYW1zJylcbiAgICAgIGNsaWVudFBhcmFtcyA9IGNyZWF0ZVJlbmRlclBhcmFtc0Zyb21DbGllbnQocGFyYW1zLCBzdG9yZSlcbiAgICB9XG5cbiAgICByZXR1cm4gPENvbXBvbmVudCBwYXJhbXM9e2NsaWVudFBhcmFtc30gc2VhcmNoUGFyYW1zPXtjbGllbnRTZWFyY2hQYXJhbXN9IC8+XG4gIH0gZWxzZSB7XG4gICAgY29uc3QgeyBjcmVhdGVSZW5kZXJTZWFyY2hQYXJhbXNGcm9tQ2xpZW50IH0gPVxuICAgICAgcmVxdWlyZSgnLi4vLi4vc2VydmVyL3JlcXVlc3Qvc2VhcmNoLXBhcmFtcy5icm93c2VyJykgYXMgdHlwZW9mIGltcG9ydCgnLi4vLi4vc2VydmVyL3JlcXVlc3Qvc2VhcmNoLXBhcmFtcy5icm93c2VyJylcbiAgICBjb25zdCBjbGllbnRTZWFyY2hQYXJhbXMgPSBjcmVhdGVSZW5kZXJTZWFyY2hQYXJhbXNGcm9tQ2xpZW50KHNlYXJjaFBhcmFtcylcbiAgICBjb25zdCB7IGNyZWF0ZVJlbmRlclBhcmFtc0Zyb21DbGllbnQgfSA9XG4gICAgICByZXF1aXJlKCcuLi8uLi9zZXJ2ZXIvcmVxdWVzdC9wYXJhbXMuYnJvd3NlcicpIGFzIHR5cGVvZiBpbXBvcnQoJy4uLy4uL3NlcnZlci9yZXF1ZXN0L3BhcmFtcy5icm93c2VyJylcbiAgICBjb25zdCBjbGllbnRQYXJhbXMgPSBjcmVhdGVSZW5kZXJQYXJhbXNGcm9tQ2xpZW50KHBhcmFtcylcblxuICAgIHJldHVybiA8Q29tcG9uZW50IHBhcmFtcz17Y2xpZW50UGFyYW1zfSBzZWFyY2hQYXJhbXM9e2NsaWVudFNlYXJjaFBhcmFtc30gLz5cbiAgfVxufVxuIl0sIm5hbWVzIjpbIkNsaWVudFBhZ2VSb290IiwiQ29tcG9uZW50Iiwic2VhcmNoUGFyYW1zIiwicGFyYW1zIiwicHJvbWlzZXMiLCJ3aW5kb3ciLCJ3b3JrQXN5bmNTdG9yYWdlIiwicmVxdWlyZSIsImNsaWVudFNlYXJjaFBhcmFtcyIsImNsaWVudFBhcmFtcyIsInN0b3JlIiwiZ2V0U3RvcmUiLCJJbnZhcmlhbnRFcnJvciIsImlzU3RhdGljR2VuZXJhdGlvbiIsImNyZWF0ZVByZXJlbmRlclNlYXJjaFBhcmFtc0Zyb21DbGllbnQiLCJjcmVhdGVQcmVyZW5kZXJQYXJhbXNGcm9tQ2xpZW50IiwiY3JlYXRlUmVuZGVyU2VhcmNoUGFyYW1zRnJvbUNsaWVudCIsImNyZWF0ZVJlbmRlclBhcmFtc0Zyb21DbGllbnQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":
/*!********************************************************************!*\
  !*** ./node_modules/next/dist/client/components/client-segment.js ***!
  \********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"ClientSegmentRoot\", ({\n    enumerable: true,\n    get: function() {\n        return ClientSegmentRoot;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _invarianterror = __webpack_require__(/*! ../../shared/lib/invariant-error */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/invariant-error.js\");\nfunction ClientSegmentRoot(param) {\n    let { Component, slots, params, promise } = param;\n    if (typeof window === 'undefined') {\n        const { workAsyncStorage } = __webpack_require__(/*! ../../server/app-render/work-async-storage.external */ \"(shared)/./node_modules/next/dist/server/app-render/work-async-storage.external.js\");\n        let clientParams;\n        // We are going to instrument the searchParams prop with tracking for the\n        // appropriate context. We wrap differently in prerendering vs rendering\n        const store = workAsyncStorage.getStore();\n        if (!store) {\n            throw new _invarianterror.InvariantError('Expected workStore to exist when handling params in a client segment such as a Layout or Template.');\n        }\n        const { createPrerenderParamsFromClient } = __webpack_require__(/*! ../../server/request/params */ \"(app-pages-browser)/./node_modules/next/dist/server/request/params.js\");\n        if (store.isStaticGeneration) {\n            clientParams = createPrerenderParamsFromClient(params, store);\n        } else {\n            const { createRenderParamsFromClient } = __webpack_require__(/*! ../../server/request/params */ \"(app-pages-browser)/./node_modules/next/dist/server/request/params.js\");\n            clientParams = createRenderParamsFromClient(params, store);\n        }\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(Component, {\n            ...slots,\n            params: clientParams\n        });\n    } else {\n        const { createRenderParamsFromClient } = __webpack_require__(/*! ../../server/request/params.browser */ \"(app-pages-browser)/./node_modules/next/dist/server/request/params.browser.js\");\n        const clientParams = createRenderParamsFromClient(params);\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(Component, {\n            ...slots,\n            params: clientParams\n        });\n    }\n}\n_c = ClientSegmentRoot;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=client-segment.js.map\nvar _c;\n$RefreshReg$(_c, \"ClientSegmentRoot\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next/dist/client/components/layout-router.js ***!
  \*******************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return OuterLayoutRouter;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _reactdom = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\"));\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _fetchserverresponse = __webpack_require__(/*! ./router-reducer/fetch-server-response */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\");\nconst _unresolvedthenable = __webpack_require__(/*! ./unresolved-thenable */ \"(app-pages-browser)/./node_modules/next/dist/client/components/unresolved-thenable.js\");\nconst _errorboundary = __webpack_require__(/*! ./error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\");\nconst _matchsegments = __webpack_require__(/*! ./match-segments */ \"(app-pages-browser)/./node_modules/next/dist/client/components/match-segments.js\");\nconst _handlesmoothscroll = __webpack_require__(/*! ../../shared/lib/router/utils/handle-smooth-scroll */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js\");\nconst _redirectboundary = __webpack_require__(/*! ./redirect-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect-boundary.js\");\nconst _notfoundboundary = __webpack_require__(/*! ./not-found-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js\");\nconst _getsegmentvalue = __webpack_require__(/*! ./router-reducer/reducers/get-segment-value */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/get-segment-value.js\");\nconst _createroutercachekey = __webpack_require__(/*! ./router-reducer/create-router-cache-key */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-router-cache-key.js\");\nconst _hasinterceptionrouteincurrenttree = __webpack_require__(/*! ./router-reducer/reducers/has-interception-route-in-current-tree */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/has-interception-route-in-current-tree.js\");\n/**\n * Add refetch marker to router state at the point of the current layout segment.\n * This ensures the response returned is not further down than the current layout segment.\n */ function walkAddRefetch(segmentPathToWalk, treeToRecreate) {\n    if (segmentPathToWalk) {\n        const [segment, parallelRouteKey] = segmentPathToWalk;\n        const isLast = segmentPathToWalk.length === 2;\n        if ((0, _matchsegments.matchSegment)(treeToRecreate[0], segment)) {\n            if (treeToRecreate[1].hasOwnProperty(parallelRouteKey)) {\n                if (isLast) {\n                    const subTree = walkAddRefetch(undefined, treeToRecreate[1][parallelRouteKey]);\n                    return [\n                        treeToRecreate[0],\n                        {\n                            ...treeToRecreate[1],\n                            [parallelRouteKey]: [\n                                subTree[0],\n                                subTree[1],\n                                subTree[2],\n                                'refetch'\n                            ]\n                        }\n                    ];\n                }\n                return [\n                    treeToRecreate[0],\n                    {\n                        ...treeToRecreate[1],\n                        [parallelRouteKey]: walkAddRefetch(segmentPathToWalk.slice(2), treeToRecreate[1][parallelRouteKey])\n                    }\n                ];\n            }\n        }\n    }\n    return treeToRecreate;\n}\nconst __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE = _reactdom.default.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;\n// TODO-APP: Replace with new React API for finding dom nodes without a `ref` when available\n/**\n * Wraps ReactDOM.findDOMNode with additional logic to hide React Strict Mode warning\n */ function findDOMNode(instance) {\n    // Tree-shake for server bundle\n    if (typeof window === 'undefined') return null;\n    // __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE.findDOMNode is null during module init.\n    // We need to lazily reference it.\n    const internal_reactDOMfindDOMNode = __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE.findDOMNode;\n    return internal_reactDOMfindDOMNode(instance);\n}\nconst rectProperties = [\n    'bottom',\n    'height',\n    'left',\n    'right',\n    'top',\n    'width',\n    'x',\n    'y'\n];\n/**\n * Check if a HTMLElement is hidden or fixed/sticky position\n */ function shouldSkipElement(element) {\n    // we ignore fixed or sticky positioned elements since they'll likely pass the \"in-viewport\" check\n    // and will result in a situation we bail on scroll because of something like a fixed nav,\n    // even though the actual page content is offscreen\n    if ([\n        'sticky',\n        'fixed'\n    ].includes(getComputedStyle(element).position)) {\n        if (true) {\n            console.warn('Skipping auto-scroll behavior due to `position: sticky` or `position: fixed` on element:', element);\n        }\n        return true;\n    }\n    // Uses `getBoundingClientRect` to check if the element is hidden instead of `offsetParent`\n    // because `offsetParent` doesn't consider document/body\n    const rect = element.getBoundingClientRect();\n    return rectProperties.every((item)=>rect[item] === 0);\n}\n/**\n * Check if the top corner of the HTMLElement is in the viewport.\n */ function topOfElementInViewport(element, viewportHeight) {\n    const rect = element.getBoundingClientRect();\n    return rect.top >= 0 && rect.top <= viewportHeight;\n}\n/**\n * Find the DOM node for a hash fragment.\n * If `top` the page has to scroll to the top of the page. This mirrors the browser's behavior.\n * If the hash fragment is an id, the page has to scroll to the element with that id.\n * If the hash fragment is a name, the page has to scroll to the first element with that name.\n */ function getHashFragmentDomNode(hashFragment) {\n    // If the hash fragment is `top` the page has to scroll to the top of the page.\n    if (hashFragment === 'top') {\n        return document.body;\n    }\n    var _document_getElementById;\n    // If the hash fragment is an id, the page has to scroll to the element with that id.\n    return (_document_getElementById = document.getElementById(hashFragment)) != null ? _document_getElementById : document.getElementsByName(hashFragment)[0];\n}\nclass InnerScrollAndFocusHandler extends _react.default.Component {\n    componentDidMount() {\n        this.handlePotentialScroll();\n    }\n    componentDidUpdate() {\n        // Because this property is overwritten in handlePotentialScroll it's fine to always run it when true as it'll be set to false for subsequent renders.\n        if (this.props.focusAndScrollRef.apply) {\n            this.handlePotentialScroll();\n        }\n    }\n    render() {\n        return this.props.children;\n    }\n    constructor(...args){\n        super(...args);\n        this.handlePotentialScroll = ()=>{\n            // Handle scroll and focus, it's only applied once in the first useEffect that triggers that changed.\n            const { focusAndScrollRef, segmentPath } = this.props;\n            if (focusAndScrollRef.apply) {\n                // segmentPaths is an array of segment paths that should be scrolled to\n                // if the current segment path is not in the array, the scroll is not applied\n                // unless the array is empty, in which case the scroll is always applied\n                if (focusAndScrollRef.segmentPaths.length !== 0 && !focusAndScrollRef.segmentPaths.some((scrollRefSegmentPath)=>segmentPath.every((segment, index)=>(0, _matchsegments.matchSegment)(segment, scrollRefSegmentPath[index])))) {\n                    return;\n                }\n                let domNode = null;\n                const hashFragment = focusAndScrollRef.hashFragment;\n                if (hashFragment) {\n                    domNode = getHashFragmentDomNode(hashFragment);\n                }\n                // `findDOMNode` is tricky because it returns just the first child if the component is a fragment.\n                // This already caused a bug where the first child was a <link/> in head.\n                if (!domNode) {\n                    domNode = findDOMNode(this);\n                }\n                // If there is no DOM node this layout-router level is skipped. It'll be handled higher-up in the tree.\n                if (!(domNode instanceof Element)) {\n                    return;\n                }\n                // Verify if the element is a HTMLElement and if we want to consider it for scroll behavior.\n                // If the element is skipped, try to select the next sibling and try again.\n                while(!(domNode instanceof HTMLElement) || shouldSkipElement(domNode)){\n                    // No siblings found that match the criteria are found, so handle scroll higher up in the tree instead.\n                    if (domNode.nextElementSibling === null) {\n                        return;\n                    }\n                    domNode = domNode.nextElementSibling;\n                }\n                // State is mutated to ensure that the focus and scroll is applied only once.\n                focusAndScrollRef.apply = false;\n                focusAndScrollRef.hashFragment = null;\n                focusAndScrollRef.segmentPaths = [];\n                (0, _handlesmoothscroll.handleSmoothScroll)(()=>{\n                    // In case of hash scroll, we only need to scroll the element into view\n                    if (hashFragment) {\n                        domNode.scrollIntoView();\n                        return;\n                    }\n                    // Store the current viewport height because reading `clientHeight` causes a reflow,\n                    // and it won't change during this function.\n                    const htmlElement = document.documentElement;\n                    const viewportHeight = htmlElement.clientHeight;\n                    // If the element's top edge is already in the viewport, exit early.\n                    if (topOfElementInViewport(domNode, viewportHeight)) {\n                        return;\n                    }\n                    // Otherwise, try scrolling go the top of the document to be backward compatible with pages\n                    // scrollIntoView() called on `<html/>` element scrolls horizontally on chrome and firefox (that shouldn't happen)\n                    // We could use it to scroll horizontally following RTL but that also seems to be broken - it will always scroll left\n                    // scrollLeft = 0 also seems to ignore RTL and manually checking for RTL is too much hassle so we will scroll just vertically\n                    htmlElement.scrollTop = 0;\n                    // Scroll to domNode if domNode is not in viewport when scrolled to top of document\n                    if (!topOfElementInViewport(domNode, viewportHeight)) {\n                        domNode.scrollIntoView();\n                    }\n                }, {\n                    // We will force layout by querying domNode position\n                    dontForceLayout: true,\n                    onlyHashChange: focusAndScrollRef.onlyHashChange\n                });\n                // Mutate after scrolling so that it can be read by `handleSmoothScroll`\n                focusAndScrollRef.onlyHashChange = false;\n                // Set focus on the element\n                domNode.focus();\n            }\n        };\n    }\n}\nfunction ScrollAndFocusHandler(param) {\n    let { segmentPath, children } = param;\n    const context = (0, _react.useContext)(_approutercontextsharedruntime.GlobalLayoutRouterContext);\n    if (!context) {\n        throw new Error('invariant global layout router not mounted');\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(InnerScrollAndFocusHandler, {\n        segmentPath: segmentPath,\n        focusAndScrollRef: context.focusAndScrollRef,\n        children: children\n    });\n}\n_c = ScrollAndFocusHandler;\n/**\n * InnerLayoutRouter handles rendering the provided segment based on the cache.\n */ function InnerLayoutRouter(param) {\n    let { parallelRouterKey, url, childNodes, segmentPath, tree, // isActive,\n    cacheKey } = param;\n    const context = (0, _react.useContext)(_approutercontextsharedruntime.GlobalLayoutRouterContext);\n    if (!context) {\n        throw new Error('invariant global layout router not mounted');\n    }\n    const { buildId, changeByServerResponse, tree: fullTree } = context;\n    // Read segment path from the parallel router cache node.\n    let childNode = childNodes.get(cacheKey);\n    // When data is not available during rendering client-side we need to fetch\n    // it from the server.\n    if (childNode === undefined) {\n        const newLazyCacheNode = {\n            lazyData: null,\n            rsc: null,\n            prefetchRsc: null,\n            head: null,\n            prefetchHead: null,\n            parallelRoutes: new Map(),\n            loading: null\n        };\n        /**\n     * Flight data fetch kicked off during render and put into the cache.\n     */ childNode = newLazyCacheNode;\n        childNodes.set(cacheKey, newLazyCacheNode);\n    }\n    // `rsc` represents the renderable node for this segment.\n    // If this segment has a `prefetchRsc`, it's the statically prefetched data.\n    // We should use that on initial render instead of `rsc`. Then we'll switch\n    // to `rsc` when the dynamic response streams in.\n    //\n    // If no prefetch data is available, then we go straight to rendering `rsc`.\n    const resolvedPrefetchRsc = childNode.prefetchRsc !== null ? childNode.prefetchRsc : childNode.rsc;\n    // We use `useDeferredValue` to handle switching between the prefetched and\n    // final values. The second argument is returned on initial render, then it\n    // re-renders with the first argument.\n    //\n    // @ts-expect-error The second argument to `useDeferredValue` is only\n    // available in the experimental builds. When its disabled, it will always\n    // return `rsc`.\n    const rsc = (0, _react.useDeferredValue)(childNode.rsc, resolvedPrefetchRsc);\n    // `rsc` is either a React node or a promise for a React node, except we\n    // special case `null` to represent that this segment's data is missing. If\n    // it's a promise, we need to unwrap it so we can determine whether or not the\n    // data is missing.\n    const resolvedRsc = typeof rsc === 'object' && rsc !== null && typeof rsc.then === 'function' ? (0, _react.use)(rsc) : rsc;\n    if (!resolvedRsc) {\n        // The data for this segment is not available, and there's no pending\n        // navigation that will be able to fulfill it. We need to fetch more from\n        // the server and patch the cache.\n        // Check if there's already a pending request.\n        let lazyData = childNode.lazyData;\n        if (lazyData === null) {\n            /**\n       * Router state with refetch marker added\n       */ // TODO-APP: remove ''\n            const refetchTree = walkAddRefetch([\n                '',\n                ...segmentPath\n            ], fullTree);\n            const includeNextUrl = (0, _hasinterceptionrouteincurrenttree.hasInterceptionRouteInCurrentTree)(fullTree);\n            childNode.lazyData = lazyData = (0, _fetchserverresponse.fetchServerResponse)(new URL(url, location.origin), {\n                flightRouterState: refetchTree,\n                nextUrl: includeNextUrl ? context.nextUrl : null,\n                buildId\n            }).then((serverResponse)=>{\n                (0, _react.startTransition)(()=>{\n                    changeByServerResponse({\n                        previousTree: fullTree,\n                        serverResponse\n                    });\n                });\n                return serverResponse;\n            });\n        }\n        // Suspend infinitely as `changeByServerResponse` will cause a different part of the tree to be rendered.\n        // A falsey `resolvedRsc` indicates missing data -- we should not commit that branch, and we need to wait for the data to arrive.\n        (0, _react.use)(_unresolvedthenable.unresolvedThenable);\n    }\n    // If we get to this point, then we know we have something we can render.\n    const subtree = /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.LayoutRouterContext.Provider, {\n        value: {\n            tree: tree[1][parallelRouterKey],\n            childNodes: childNode.parallelRoutes,\n            // TODO-APP: overriding of url for parallel routes\n            url: url,\n            loading: childNode.loading\n        },\n        children: resolvedRsc\n    });\n    // Ensure root layout is not wrapped in a div as the root layout renders `<html>`\n    return subtree;\n}\n_c1 = InnerLayoutRouter;\n/**\n * Renders suspense boundary with the provided \"loading\" property as the fallback.\n * If no loading property is provided it renders the children without a suspense boundary.\n */ function LoadingBoundary(param) {\n    let { children, hasLoading, loading, loadingStyles, loadingScripts } = param;\n    // We have an explicit prop for checking if `loading` is provided, to disambiguate between a loading\n    // component that returns `null` / `undefined`, vs not having a loading component at all.\n    if (hasLoading) {\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(_react.Suspense, {\n            fallback: /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                children: [\n                    loadingStyles,\n                    loadingScripts,\n                    loading\n                ]\n            }),\n            children: children\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {\n        children: children\n    });\n}\n_c2 = LoadingBoundary;\nfunction OuterLayoutRouter(param) {\n    let { parallelRouterKey, segmentPath, error, errorStyles, errorScripts, templateStyles, templateScripts, template, notFound, notFoundStyles } = param;\n    const context = (0, _react.useContext)(_approutercontextsharedruntime.LayoutRouterContext);\n    if (!context) {\n        throw new Error('invariant expected layout router to be mounted');\n    }\n    const { childNodes, tree, url, loading } = context;\n    // Get the current parallelRouter cache node\n    let childNodesForParallelRouter = childNodes.get(parallelRouterKey);\n    // If the parallel router cache node does not exist yet, create it.\n    // This writes to the cache when there is no item in the cache yet. It never *overwrites* existing cache items which is why it's safe in concurrent mode.\n    if (!childNodesForParallelRouter) {\n        childNodesForParallelRouter = new Map();\n        childNodes.set(parallelRouterKey, childNodesForParallelRouter);\n    }\n    // Get the active segment in the tree\n    // The reason arrays are used in the data format is that these are transferred from the server to the browser so it's optimized to save bytes.\n    const treeSegment = tree[1][parallelRouterKey][0];\n    // If segment is an array it's a dynamic route and we want to read the dynamic route value as the segment to get from the cache.\n    const currentChildSegmentValue = (0, _getsegmentvalue.getSegmentValue)(treeSegment);\n    /**\n   * Decides which segments to keep rendering, all segments that are not active will be wrapped in `<Offscreen>`.\n   */ // TODO-APP: Add handling of `<Offscreen>` when it's available.\n    const preservedSegments = [\n        treeSegment\n    ];\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {\n        children: preservedSegments.map((preservedSegment)=>{\n            const preservedSegmentValue = (0, _getsegmentvalue.getSegmentValue)(preservedSegment);\n            const cacheKey = (0, _createroutercachekey.createRouterCacheKey)(preservedSegment);\n            return(/*\n            - Error boundary\n              - Only renders error boundary if error component is provided.\n              - Rendered for each segment to ensure they have their own error state.\n            - Loading boundary\n              - Only renders suspense boundary if loading components is provided.\n              - Rendered for each segment to ensure they have their own loading state.\n              - Passed to the router during rendering to ensure it can be immediately rendered when suspending on a Flight fetch.\n          */ /*#__PURE__*/ (0, _jsxruntime.jsxs)(_approutercontextsharedruntime.TemplateContext.Provider, {\n                value: /*#__PURE__*/ (0, _jsxruntime.jsx)(ScrollAndFocusHandler, {\n                    segmentPath: segmentPath,\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_errorboundary.ErrorBoundary, {\n                        errorComponent: error,\n                        errorStyles: errorStyles,\n                        errorScripts: errorScripts,\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(LoadingBoundary, {\n                            hasLoading: Boolean(loading),\n                            loading: loading == null ? void 0 : loading[0],\n                            loadingStyles: loading == null ? void 0 : loading[1],\n                            loadingScripts: loading == null ? void 0 : loading[2],\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_notfoundboundary.NotFoundBoundary, {\n                                notFound: notFound,\n                                notFoundStyles: notFoundStyles,\n                                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_redirectboundary.RedirectBoundary, {\n                                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(InnerLayoutRouter, {\n                                        parallelRouterKey: parallelRouterKey,\n                                        url: url,\n                                        tree: tree,\n                                        childNodes: childNodesForParallelRouter,\n                                        segmentPath: segmentPath,\n                                        cacheKey: cacheKey,\n                                        isActive: currentChildSegmentValue === preservedSegmentValue\n                                    })\n                                })\n                            })\n                        })\n                    })\n                }),\n                children: [\n                    templateStyles,\n                    templateScripts,\n                    template\n                ]\n            }, (0, _createroutercachekey.createRouterCacheKey)(preservedSegment, true)));\n        })\n    });\n}\n_c3 = OuterLayoutRouter;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=layout-router.js.map\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"ScrollAndFocusHandler\");\n$RefreshReg$(_c1, \"InnerLayoutRouter\");\n$RefreshReg$(_c2, \"LoadingBoundary\");\n$RefreshReg$(_c3, \"OuterLayoutRouter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/render-from-template-context.js ***!
  \**********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return RenderFromTemplateContext;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nfunction RenderFromTemplateContext() {\n    const children = (0, _react.useContext)(_approutercontextsharedruntime.TemplateContext);\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {\n        children: children\n    });\n}\n_c = RenderFromTemplateContext;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=render-from-template-context.js.map\nvar _c;\n$RefreshReg$(_c, \"RenderFromTemplateContext\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7ZUFLd0JBOzs7Ozs7K0NBRlE7QUFFakIsU0FBU0EsOEJBQUFBLG1CQUFBQSxDQUFBQSx3SkFBQUE7SUFDdEI7SUFDQSxxQkFBTztXQUFHQyxPQUFBQSxJQUFBQSxHQUFBQSxDQUFBQSxHQUFBQSxZQUFBQSxHQUFBQSxFQUFBQSxZQUFBQSxRQUFBQSxFQUFBQTs7SUFDWjs7S0FGRSxDQUFNQSxXQUFXQyxJQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vLi4vc3JjL2NsaWVudC9jb21wb25lbnRzL3JlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQudHN4PzlhYzIiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCBSZWFjdCwgeyB1c2VDb250ZXh0LCB0eXBlIEpTWCB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgVGVtcGxhdGVDb250ZXh0IH0gZnJvbSAnLi4vLi4vc2hhcmVkL2xpYi9hcHAtcm91dGVyLWNvbnRleHQuc2hhcmVkLXJ1bnRpbWUnXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJlbmRlckZyb21UZW1wbGF0ZUNvbnRleHQoKTogSlNYLkVsZW1lbnQge1xuICBjb25zdCBjaGlsZHJlbiA9IHVzZUNvbnRleHQoVGVtcGxhdGVDb250ZXh0KVxuICByZXR1cm4gPD57Y2hpbGRyZW59PC8+XG59XG4iXSwibmFtZXMiOlsiUmVuZGVyRnJvbVRlbXBsYXRlQ29udGV4dCIsImNoaWxkcmVuIiwidXNlQ29udGV4dCIsIlRlbXBsYXRlQ29udGV4dCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/invariant-error.js":
/*!**************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/invariant-error.js ***!
  \**************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"InvariantError\", ({\n    enumerable: true,\n    get: function() {\n        return InvariantError;\n    }\n}));\nclass InvariantError extends Error {\n    constructor(message, options){\n        super(\"Invariant: \" + (message.endsWith('.') ? message : message + '.') + \" This is a bug in Next.js.\", options);\n        this.name = 'InvariantError';\n    }\n} //# sourceMappingURL=invariant-error.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9pbnZhcmlhbnQtZXJyb3IuanMiLCJtYXBwaW5ncyI6Ijs7OztrREFBYUE7OztlQUFBQTs7O0FBQU4sTUFBTUEsdUJBQXVCQztJQUNsQ0MsWUFBWUMsT0FBZSxFQUFFQyxPQUFzQixDQUFFO1FBQ25ELEtBQUssQ0FDRixnQkFBYUQsQ0FBQUEsUUFBUUUsUUFBUSxDQUFDLE9BQU9GLFVBQVVBLFVBQVUsSUFBRSxHQUFFLDhCQUM5REM7UUFFRixJQUFJLENBQUNFLElBQUksR0FBRztJQUNkO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uLy4uL3NyYy9zaGFyZWQvbGliL2ludmFyaWFudC1lcnJvci50cz9iNzczIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjbGFzcyBJbnZhcmlhbnRFcnJvciBleHRlbmRzIEVycm9yIHtcbiAgY29uc3RydWN0b3IobWVzc2FnZTogc3RyaW5nLCBvcHRpb25zPzogRXJyb3JPcHRpb25zKSB7XG4gICAgc3VwZXIoXG4gICAgICBgSW52YXJpYW50OiAke21lc3NhZ2UuZW5kc1dpdGgoJy4nKSA/IG1lc3NhZ2UgOiBtZXNzYWdlICsgJy4nfSBUaGlzIGlzIGEgYnVnIGluIE5leHQuanMuYCxcbiAgICAgIG9wdGlvbnNcbiAgICApXG4gICAgdGhpcy5uYW1lID0gJ0ludmFyaWFudEVycm9yJ1xuICB9XG59XG4iXSwibmFtZXMiOlsiSW52YXJpYW50RXJyb3IiLCJFcnJvciIsImNvbnN0cnVjdG9yIiwibWVzc2FnZSIsIm9wdGlvbnMiLCJlbmRzV2l0aCIsIm5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/invariant-error.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js":
/*!********************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js ***!
  \********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * Run function with `scroll-behavior: auto` applied to `<html/>`.\n * This css change will be reverted after the function finishes.\n */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"handleSmoothScroll\", ({\n    enumerable: true,\n    get: function() {\n        return handleSmoothScroll;\n    }\n}));\nfunction handleSmoothScroll(fn, options) {\n    if (options === void 0) options = {};\n    // if only the hash is changed, we don't need to disable smooth scrolling\n    // we only care to prevent smooth scrolling when navigating to a new page to avoid jarring UX\n    if (options.onlyHashChange) {\n        fn();\n        return;\n    }\n    const htmlElement = document.documentElement;\n    const existing = htmlElement.style.scrollBehavior;\n    htmlElement.style.scrollBehavior = 'auto';\n    if (!options.dontForceLayout) {\n        // In Chrome-based browsers we need to force reflow before calling `scrollTo`.\n        // Otherwise it will not pickup the change in scrollBehavior\n        // More info here: https://github.com/vercel/next.js/issues/40719#issuecomment-1336248042\n        htmlElement.getClientRects();\n    }\n    fn();\n    htmlElement.style.scrollBehavior = existing;\n} //# sourceMappingURL=handle-smooth-scroll.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcoryjanowski%2FProjects%2Fflow%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);