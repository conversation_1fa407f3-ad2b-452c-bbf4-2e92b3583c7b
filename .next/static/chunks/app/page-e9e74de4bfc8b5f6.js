(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[931],{7535:(e,t,a)=>{Promise.resolve().then(a.bind(a,486))},486:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>u});var s=a(7437),l=a(2265),n=a(5916);function r(e){let{onGenerate:t,isGenerating:a}=e,[r,i]=(0,l.useState)("");return(0,s.jsxs)(n.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"glass-effect p-8 rounded-lg max-w-md w-full",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-center mb-6",children:"Generate Your Flow"}),(0,s.jsxs)("form",{onSubmit:e=>{e.preventDefault(),r.trim()&&!a&&t(r.trim())},className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"prompt",className:"block text-sm font-medium text-gray-300 mb-2",children:"Describe your ideal coding atmosphere"}),(0,s.jsx)("textarea",{id:"prompt",value:r,onChange:e=>i(e.target.value),placeholder:"e.g., Peaceful rain sounds with soft piano for deep focus...",className:"w-full p-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 resize-none",rows:3,disabled:a})]}),(0,s.jsx)("button",{type:"submit",disabled:!r.trim()||a,className:"w-full bg-flow-accent hover:bg-flow-accent/80 disabled:bg-gray-600 disabled:cursor-not-allowed text-white py-3 px-6 rounded-lg font-medium transition-colors",children:a?"Generating Flow...":"Generate Flow"})]}),(0,s.jsxs)("div",{className:"mt-6",children:[(0,s.jsx)("p",{className:"text-sm text-gray-400 mb-3",children:"Or try a preset:"}),(0,s.jsx)("div",{className:"space-y-2",children:["Rainy day coding session","Cyberpunk neon vibes","Forest morning ambience","Retro 8-bit soundscape","Deep focus meditation"].map((e,t)=>(0,s.jsx)("button",{onClick:()=>i(e),disabled:a,className:"w-full text-left p-2 text-sm bg-white/5 hover:bg-white/10 disabled:hover:bg-white/5 rounded border border-white/10 transition-colors",children:e},t))})]})]})}let i=(0,a(7818).default)(()=>Promise.all([a.e(689),a.e(777),a.e(15)]).then(a.bind(a,5015)),{loadableGenerated:{webpack:()=>[5015]},ssr:!1,loading:()=>(0,s.jsx)("div",{className:"absolute inset-0 gradient-bg"})});function o(e){let{prompt:t}=e,[a,n]=(0,l.useState)(!1);return((0,l.useEffect)(()=>{n(!0)},[]),a)?(0,s.jsx)("div",{className:"absolute inset-0 gradient-bg",children:(0,s.jsx)(i,{prompt:t})}):(0,s.jsx)("div",{className:"absolute inset-0 gradient-bg"})}function c(e){let{audioUrl:t,prompt:a,onNewFlow:r,isFlowMode:i,onToggleFlowMode:o}=e,[c,d]=(0,l.useState)(!1),[x,m]=(0,l.useState)(.7),[u,h]=(0,l.useState)(0),[p,f]=(0,l.useState)(0),g=(0,l.useRef)(null);(0,l.useEffect)(()=>{let e=g.current;if(!e)return;let t=()=>h(e.currentTime),a=()=>f(e.duration);return e.addEventListener("timeupdate",t),e.addEventListener("loadedmetadata",a),e.addEventListener("ended",()=>d(!1)),()=>{e.removeEventListener("timeupdate",t),e.removeEventListener("loadedmetadata",a),e.removeEventListener("ended",()=>d(!1))}},[t]);let b=e=>{let t=Math.floor(e/60),a=Math.floor(e%60);return"".concat(t,":").concat(a.toString().padStart(2,"0"))};return(0,s.jsxs)(n.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"glass-effect p-6 rounded-lg max-w-md w-full ".concat(i?"fixed bottom-4 right-4 max-w-xs":""),children:[(0,s.jsx)("audio",{ref:g,src:t}),!i&&(0,s.jsxs)("div",{className:"text-center mb-4",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Now Playing"}),(0,s.jsx)("p",{className:"text-sm text-gray-300 truncate",children:a})]}),(0,s.jsxs)("div",{className:"flex items-center gap-4 mb-4",children:[(0,s.jsx)("button",{onClick:()=>{let e=g.current;e&&(c?e.pause():e.play(),d(!c))},className:"bg-flow-accent hover:bg-flow-accent/80 text-white p-3 rounded-full transition-colors",children:c?(0,s.jsx)("svg",{className:"w-6 h-6",fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z",clipRule:"evenodd"})}):(0,s.jsx)("svg",{className:"w-6 h-6",fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z",clipRule:"evenodd"})})}),(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-300",children:[(0,s.jsx)("span",{children:b(u)}),(0,s.jsx)("div",{className:"flex-1 bg-gray-600 rounded-full h-1",children:(0,s.jsx)("div",{className:"bg-flow-accent h-1 rounded-full transition-all",style:{width:"".concat(p?u/p*100:0,"%")}})}),(0,s.jsx)("span",{children:b(p)})]})})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,s.jsx)("svg",{className:"w-4 h-4 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.824L4.5 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.5l3.883-3.824z",clipRule:"evenodd"})}),(0,s.jsx)("input",{type:"range",min:"0",max:"1",step:"0.1",value:x,onChange:e=>{let t=parseFloat(e.target.value);m(t),g.current&&(g.current.volume=t)},className:"flex-1"})]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)("button",{onClick:r,className:"flex-1 bg-gray-600 hover:bg-gray-500 text-white py-2 px-4 rounded transition-colors",children:"New Flow"}),(0,s.jsx)("button",{onClick:o,className:"bg-flow-accent hover:bg-flow-accent/80 text-white py-2 px-4 rounded transition-colors",children:i?"Exit Flow":"Flow Mode"})]})]})}function d(e){let{onSessionComplete:t}=e,[a,r]=(0,l.useState)(1500),[i,o]=(0,l.useState)(!1),[c,d]=(0,l.useState)(!1),[x,m]=(0,l.useState)(0);return(0,l.useEffect)(()=>{let e=null;return i&&a>0?e=setInterval(()=>{r(e=>e-1)},1e3):0===a&&(c?(r(1500),d(!1)):(m(e=>e+1),r(300),d(!0)),o(!1),null==t||t()),()=>{e&&clearInterval(e)}},[i,a,c,t]),(0,s.jsx)(n.E.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},className:"glass-effect p-4 rounded-lg",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-gray-300 mb-2",children:c?"Break Time":"Focus Session"}),(0,s.jsx)("div",{className:"text-2xl font-mono font-bold mb-4",children:(e=>{let t=Math.floor(e/60);return"".concat(t.toString().padStart(2,"0"),":").concat((e%60).toString().padStart(2,"0"))})(a)}),(0,s.jsxs)("div",{className:"flex gap-2 justify-center mb-3",children:[(0,s.jsx)("button",{onClick:()=>o(!i),className:"bg-flow-accent hover:bg-flow-accent/80 text-white px-4 py-2 rounded text-sm transition-colors",children:i?"Pause":"Start"}),(0,s.jsx)("button",{onClick:()=>{o(!1),r(1500),d(!1)},className:"bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded text-sm transition-colors",children:"Reset"})]}),(0,s.jsxs)("div",{className:"text-xs text-gray-400",children:["Sessions completed: ",x]})]})})}function x(){let[e,t]=(0,l.useState)([]),[a,r]=(0,l.useState)(""),i=a=>{t(e.map(e=>e.id===a?{...e,completed:!e.completed}:e))},o=a=>{t(e.filter(e=>e.id!==a))};return(0,s.jsxs)(n.E.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},className:"glass-effect p-4 rounded-lg w-80",children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-gray-300 mb-3",children:"Session Tasks"}),(0,s.jsx)("form",{onSubmit:s=>{s.preventDefault(),a.trim()&&(t([...e,{id:Date.now(),text:a.trim(),completed:!1}]),r(""))},className:"mb-3",children:(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)("input",{type:"text",value:a,onChange:e=>r(e.target.value),placeholder:"Add a task...",className:"flex-1 p-2 text-sm bg-white/10 border border-white/20 rounded text-white placeholder-gray-400"}),(0,s.jsx)("button",{type:"submit",className:"bg-flow-accent hover:bg-flow-accent/80 text-white px-3 py-2 rounded text-sm transition-colors",children:"Add"})]})}),(0,s.jsx)("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:e.map(e=>(0,s.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-white/5 rounded",children:[(0,s.jsx)("input",{type:"checkbox",checked:e.completed,onChange:()=>i(e.id),className:"rounded"}),(0,s.jsx)("span",{className:"flex-1 text-sm ".concat(e.completed?"line-through text-gray-400":"text-white"),children:e.text}),(0,s.jsx)("button",{onClick:()=>o(e.id),className:"text-red-400 hover:text-red-300 text-sm",children:"\xd7"})]},e.id))}),0===e.length&&(0,s.jsx)("p",{className:"text-gray-400 text-sm text-center py-4",children:"No tasks yet. Add one above!"})]})}function m(){return(0,s.jsxs)(n.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"fixed bottom-6 left-6 right-6 flex gap-4 justify-center",children:[(0,s.jsx)(d,{}),(0,s.jsx)(x,{})]})}function u(){let[e,t]=(0,l.useState)(""),[a,n]=(0,l.useState)(!1),[i,d]=(0,l.useState)(null),[x,u]=(0,l.useState)(!1),h=async e=>{t(e),n(!0);try{let t=await fetch("/api/generate-audio",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({prompt:e})});if(!t.ok)throw Error("Failed to generate audio");let a=await t.json();d(a.audioUrl)}catch(e){console.error("Failed to generate flow:",e)}finally{n(!1)}};return(0,s.jsxs)("main",{className:"relative h-screen w-full overflow-hidden",children:[(0,s.jsx)(o,{prompt:e}),(0,s.jsxs)("div",{className:"relative z-10 h-full flex flex-col",children:[!x&&(0,s.jsx)("header",{className:"p-6",children:(0,s.jsxs)("h1",{className:"text-3xl font-bold text-center mb-8",children:["Code",(0,s.jsx)("span",{className:"text-flow-accent",children:"Flow"})]})}),(0,s.jsx)("div",{className:"flex-1 flex items-center justify-center px-6",children:i?(0,s.jsx)(c,{audioUrl:i,prompt:e,onNewFlow:()=>d(null),isFlowMode:x,onToggleFlowMode:()=>u(!x)}):(0,s.jsx)(r,{onGenerate:h,isGenerating:a})}),!x&&i&&(0,s.jsx)(m,{})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[305,130,215,744],()=>t(7535)),_N_E=e.O()}]);