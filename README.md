# CodeFlow - AI-Powered Ambient Coding Environment

Get into flow state with AI-generated soundscapes and dynamic visuals designed for programmers.

## Features

- 🎵 AI-generated ambient soundscapes based on text prompts
- 🎨 Dynamic 3D backgrounds that respond to your mood
- ⏱️ Built-in Pomodoro timer for productivity
- ✅ Session-based todo list
- 🎛️ Audio mixing controls
- 🧘 Flow mode for distraction-free coding

## Tech Stack

- **Framework**: Next.js 15 with TypeScript
- **Styling**: Tailwind CSS
- **3D Graphics**: Three.js with React Three Fiber
- **Animations**: Framer Motion
- **Deployment**: Vercel

## Getting Started

1. Clone the repository
2. Install dependencies: `npm install`
3. Run development server: `npm run dev`
4. Open [http://localhost:3000](http://localhost:3000)

## Deployment

This app is optimized for deployment on Vercel with zero configuration.

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/yourusername/codeflow)

## Future Enhancements

- Integration with Gemini API for real audio generation
- User accounts and saved flows
- Team collaboration features
- Premium soundscape library