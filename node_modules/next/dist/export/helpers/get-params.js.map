{"version": 3, "sources": ["../../../src/export/helpers/get-params.ts"], "sourcesContent": ["import {\n  type RouteMatchFn,\n  getRouteMatcher,\n} from '../../shared/lib/router/utils/route-matcher'\nimport { getRouteRegex } from '../../shared/lib/router/utils/route-regex'\n\n// The last page and matcher that this function handled.\nlet last: {\n  page: string\n  matcher: RouteMatchFn\n} | null = null\n\n/**\n * Gets the params for the provided page.\n * @param page the page that contains dynamic path parameters\n * @param pathname the pathname to match\n * @returns the matches that were found, throws otherwise\n */\nexport function getParams(page: string, pathname: string) {\n  // Because this is often called on the output of `getStaticPaths` or similar\n  // where the `page` here doesn't change, this will \"remember\" the last page\n  // it created the RegExp for. If it matches, it'll just re-use it.\n  let matcher: RouteMatchFn\n  if (last?.page === page) {\n    matcher = last.matcher\n  } else {\n    matcher = getRouteMatcher(getRouteRegex(page))\n  }\n\n  const params = matcher(pathname)\n  if (!params) {\n    throw new Error(\n      `The provided export path '${pathname}' doesn't match the '${page}' page.\\nRead more: https://nextjs.org/docs/messages/export-path-mismatch`\n    )\n  }\n\n  return params\n}\n"], "names": ["getParams", "last", "page", "pathname", "matcher", "getRouteMatcher", "getRouteRegex", "params", "Error"], "mappings": ";;;;+BAkBgBA;;;eAAAA;;;8BAfT;4BACuB;AAE9B,wDAAwD;AACxD,IAAIC,OAGO;AAQJ,SAASD,UAAUE,IAAY,EAAEC,QAAgB;IACtD,4EAA4E;IAC5E,2EAA2E;IAC3E,kEAAkE;IAClE,IAAIC;IACJ,IAAIH,CAAAA,wBAAAA,KAAMC,IAAI,MAAKA,MAAM;QACvBE,UAAUH,KAAKG,OAAO;IACxB,OAAO;QACLA,UAAUC,IAAAA,6BAAe,EAACC,IAAAA,yBAAa,EAACJ;IAC1C;IAEA,MAAMK,SAASH,QAAQD;IACvB,IAAI,CAACI,QAAQ;QACX,MAAM,IAAIC,MACR,CAAC,0BAA0B,EAAEL,SAAS,qBAAqB,EAAED,KAAK,yEAAyE,CAAC;IAEhJ;IAEA,OAAOK;AACT"}