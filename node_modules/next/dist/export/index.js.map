{"version": 3, "sources": ["../../src/export/index.ts"], "sourcesContent": ["import type {\n  ExportAppResult,\n  ExportAppOptions,\n  WorkerRenderOptsPartial,\n} from './types'\nimport { createStaticWorker, type PrerenderManifest } from '../build'\nimport type { PagesManifest } from '../build/webpack/plugins/pages-manifest-plugin'\n\nimport { bold, yellow } from '../lib/picocolors'\nimport findUp from 'next/dist/compiled/find-up'\nimport { existsSync, promises as fs } from 'fs'\n\nimport '../server/require-hook'\n\nimport { dirname, join, resolve, sep } from 'path'\nimport { formatAmpMessages } from '../build/output/index'\nimport type { AmpPageStatus } from '../build/output/index'\nimport * as Log from '../build/output/log'\nimport { RSC_SUFFIX, SSG_FALLBACK_EXPORT_ERROR } from '../lib/constants'\nimport { recursiveCopy } from '../lib/recursive-copy'\nimport {\n  BUILD_ID_FILE,\n  CLIENT_PUBLIC_FILES_PATH,\n  CLIENT_STATIC_FILES_PATH,\n  EXPORT_DETAIL,\n  EXPORT_MARKER,\n  NEXT_FONT_MANIFEST,\n  MIDDLEWARE_MANIFEST,\n  PAGES_MANIFEST,\n  PHASE_EXPORT,\n  PRERENDER_MANIFEST,\n  SERVER_DIRECTORY,\n  SERVER_REFERENCE_MANIFEST,\n  APP_PATH_ROUTES_MANIFEST,\n} from '../shared/lib/constants'\nimport loadConfig from '../server/config'\nimport type { ExportPathMap } from '../server/config-shared'\nimport { eventCliSession } from '../telemetry/events'\nimport { hasNextSupport } from '../server/ci-info'\nimport { Telemetry } from '../telemetry/storage'\nimport { normalizePagePath } from '../shared/lib/page-path/normalize-page-path'\nimport { denormalizePagePath } from '../shared/lib/page-path/denormalize-page-path'\nimport { loadEnvConfig } from '@next/env'\nimport { isAPIRoute } from '../lib/is-api-route'\nimport { getPagePath } from '../server/require'\nimport type { Span } from '../trace'\nimport type { MiddlewareManifest } from '../build/webpack/plugins/middleware-plugin'\nimport { isAppRouteRoute } from '../lib/is-app-route-route'\nimport { isAppPageRoute } from '../lib/is-app-page-route'\nimport isError from '../lib/is-error'\nimport { formatManifest } from '../build/manifests/formatter/format-manifest'\nimport { TurborepoAccessTraceResult } from '../build/turborepo-access-trace'\nimport { createProgress } from '../build/progress'\nimport type { DeepReadonly } from '../shared/lib/deep-readonly'\n\nexport class ExportError extends Error {\n  code = 'NEXT_EXPORT_ERROR'\n}\n\nasync function exportAppImpl(\n  dir: string,\n  options: Readonly<ExportAppOptions>,\n  span: Span\n): Promise<ExportAppResult | null> {\n  dir = resolve(dir)\n\n  // attempt to load global env values so they are available in next.config.js\n  span.traceChild('load-dotenv').traceFn(() => loadEnvConfig(dir, false, Log))\n\n  const { enabledDirectories } = options\n\n  const nextConfig =\n    options.nextConfig ||\n    (await span\n      .traceChild('load-next-config')\n      .traceAsyncFn(() => loadConfig(PHASE_EXPORT, dir)))\n\n  const distDir = join(dir, nextConfig.distDir)\n  const telemetry = options.buildExport ? null : new Telemetry({ distDir })\n\n  if (telemetry) {\n    telemetry.record(\n      eventCliSession(distDir, nextConfig, {\n        webpackVersion: null,\n        cliCommand: 'export',\n        isSrcDir: null,\n        hasNowJson: !!(await findUp('now.json', { cwd: dir })),\n        isCustomServer: null,\n        turboFlag: false,\n        pagesDir: null,\n        appDir: null,\n      })\n    )\n  }\n\n  const subFolders = nextConfig.trailingSlash && !options.buildExport\n\n  if (!options.silent && !options.buildExport) {\n    Log.info(`using build directory: ${distDir}`)\n  }\n\n  const buildIdFile = join(distDir, BUILD_ID_FILE)\n\n  if (!existsSync(buildIdFile)) {\n    throw new ExportError(\n      `Could not find a production build in the '${distDir}' directory. Try building your app with 'next build' before starting the static export. https://nextjs.org/docs/messages/next-export-no-build-id`\n    )\n  }\n\n  const customRoutes = ['rewrites', 'redirects', 'headers'].filter(\n    (config) => typeof nextConfig[config] === 'function'\n  )\n\n  if (!hasNextSupport && !options.buildExport && customRoutes.length > 0) {\n    Log.warn(\n      `rewrites, redirects, and headers are not applied when exporting your application, detected (${customRoutes.join(\n        ', '\n      )}). See more info here: https://nextjs.org/docs/messages/export-no-custom-routes`\n    )\n  }\n\n  const buildId = await fs.readFile(buildIdFile, 'utf8')\n\n  const pagesManifest =\n    !options.pages &&\n    (require(join(distDir, SERVER_DIRECTORY, PAGES_MANIFEST)) as PagesManifest)\n\n  let prerenderManifest: DeepReadonly<PrerenderManifest> | undefined\n  try {\n    prerenderManifest = require(join(distDir, PRERENDER_MANIFEST))\n  } catch {}\n\n  let appRoutePathManifest: Record<string, string> | undefined\n  try {\n    appRoutePathManifest = require(join(distDir, APP_PATH_ROUTES_MANIFEST))\n  } catch (err) {\n    if (\n      isError(err) &&\n      (err.code === 'ENOENT' || err.code === 'MODULE_NOT_FOUND')\n    ) {\n      // the manifest doesn't exist which will happen when using\n      // \"pages\" dir instead of \"app\" dir.\n      appRoutePathManifest = undefined\n    } else {\n      // the manifest is malformed (invalid json)\n      throw err\n    }\n  }\n\n  const excludedPrerenderRoutes = new Set<string>()\n  const pages = options.pages || Object.keys(pagesManifest)\n  const defaultPathMap: ExportPathMap = {}\n\n  let hasApiRoutes = false\n  for (const page of pages) {\n    // _document and _app are not real pages\n    // _error is exported as 404.html later on\n    // API Routes are Node.js functions\n\n    if (isAPIRoute(page)) {\n      hasApiRoutes = true\n      continue\n    }\n\n    if (page === '/_document' || page === '/_app' || page === '/_error') {\n      continue\n    }\n\n    // iSSG pages that are dynamic should not export templated version by\n    // default. In most cases, this would never work. There is no server that\n    // could run `getStaticProps`. If users make their page work lazily, they\n    // can manually add it to the `exportPathMap`.\n    if (prerenderManifest?.dynamicRoutes[page]) {\n      excludedPrerenderRoutes.add(page)\n      continue\n    }\n\n    defaultPathMap[page] = { page }\n  }\n\n  const mapAppRouteToPage = new Map<string, string>()\n  if (!options.buildExport && appRoutePathManifest) {\n    for (const [pageName, routePath] of Object.entries(appRoutePathManifest)) {\n      mapAppRouteToPage.set(routePath, pageName)\n      if (\n        isAppPageRoute(pageName) &&\n        !prerenderManifest?.routes[routePath] &&\n        !prerenderManifest?.dynamicRoutes[routePath]\n      ) {\n        defaultPathMap[routePath] = {\n          page: pageName,\n          _isAppDir: true,\n        }\n      }\n    }\n  }\n\n  // Initialize the output directory\n  const outDir = options.outdir\n\n  if (outDir === join(dir, 'public')) {\n    throw new ExportError(\n      `The 'public' directory is reserved in Next.js and can not be used as the export out directory. https://nextjs.org/docs/messages/can-not-output-to-public`\n    )\n  }\n\n  if (outDir === join(dir, 'static')) {\n    throw new ExportError(\n      `The 'static' directory is reserved in Next.js and can not be used as the export out directory. https://nextjs.org/docs/messages/can-not-output-to-static`\n    )\n  }\n\n  await fs.rm(outDir, { recursive: true, force: true })\n  await fs.mkdir(join(outDir, '_next', buildId), { recursive: true })\n\n  await fs.writeFile(\n    join(distDir, EXPORT_DETAIL),\n    formatManifest({\n      version: 1,\n      outDirectory: outDir,\n      success: false,\n    }),\n    'utf8'\n  )\n\n  // Copy static directory\n  if (!options.buildExport && existsSync(join(dir, 'static'))) {\n    if (!options.silent) {\n      Log.info('Copying \"static\" directory')\n    }\n    await span\n      .traceChild('copy-static-directory')\n      .traceAsyncFn(() =>\n        recursiveCopy(join(dir, 'static'), join(outDir, 'static'))\n      )\n  }\n\n  // Copy .next/static directory\n  if (\n    !options.buildExport &&\n    existsSync(join(distDir, CLIENT_STATIC_FILES_PATH))\n  ) {\n    if (!options.silent) {\n      Log.info('Copying \"static build\" directory')\n    }\n    await span\n      .traceChild('copy-next-static-directory')\n      .traceAsyncFn(() =>\n        recursiveCopy(\n          join(distDir, CLIENT_STATIC_FILES_PATH),\n          join(outDir, '_next', CLIENT_STATIC_FILES_PATH)\n        )\n      )\n  }\n\n  // Get the exportPathMap from the config file\n  if (typeof nextConfig.exportPathMap !== 'function') {\n    nextConfig.exportPathMap = async (defaultMap) => {\n      return defaultMap\n    }\n  }\n\n  const {\n    i18n,\n    images: { loader = 'default', unoptimized },\n  } = nextConfig\n\n  if (i18n && !options.buildExport) {\n    throw new ExportError(\n      `i18n support is not compatible with next export. See here for more info on deploying: https://nextjs.org/docs/messages/export-no-custom-routes`\n    )\n  }\n\n  if (!options.buildExport) {\n    const { isNextImageImported } = await span\n      .traceChild('is-next-image-imported')\n      .traceAsyncFn(() =>\n        fs\n          .readFile(join(distDir, EXPORT_MARKER), 'utf8')\n          .then((text) => JSON.parse(text))\n          .catch(() => ({}))\n      )\n\n    if (\n      isNextImageImported &&\n      loader === 'default' &&\n      !unoptimized &&\n      !hasNextSupport\n    ) {\n      throw new ExportError(\n        `Image Optimization using the default loader is not compatible with export.\n  Possible solutions:\n    - Use \\`next start\\` to run a server, which includes the Image Optimization API.\n    - Configure \\`images.unoptimized = true\\` in \\`next.config.js\\` to disable the Image Optimization API.\n  Read more: https://nextjs.org/docs/messages/export-image-api`\n      )\n    }\n  }\n\n  let serverActionsManifest\n  if (enabledDirectories.app) {\n    serverActionsManifest = require(\n      join(distDir, SERVER_DIRECTORY, SERVER_REFERENCE_MANIFEST + '.json')\n    )\n    if (nextConfig.output === 'export') {\n      if (\n        Object.keys(serverActionsManifest.node).length > 0 ||\n        Object.keys(serverActionsManifest.edge).length > 0\n      ) {\n        throw new ExportError(\n          `Server Actions are not supported with static export.`\n        )\n      }\n    }\n  }\n\n  // Start the rendering process\n  const renderOpts: WorkerRenderOptsPartial = {\n    previewProps: prerenderManifest?.preview,\n    buildId,\n    nextExport: true,\n    assetPrefix: nextConfig.assetPrefix.replace(/\\/$/, ''),\n    distDir,\n    dev: false,\n    basePath: nextConfig.basePath,\n    trailingSlash: nextConfig.trailingSlash,\n    canonicalBase: nextConfig.amp?.canonicalBase || '',\n    ampSkipValidation: nextConfig.experimental.amp?.skipValidation || false,\n    ampOptimizerConfig: nextConfig.experimental.amp?.optimizer || undefined,\n    locales: i18n?.locales,\n    locale: i18n?.defaultLocale,\n    defaultLocale: i18n?.defaultLocale,\n    domainLocales: i18n?.domains,\n    disableOptimizedLoading: nextConfig.experimental.disableOptimizedLoading,\n    // Exported pages do not currently support dynamic HTML.\n    supportsDynamicResponse: false,\n    crossOrigin: nextConfig.crossOrigin,\n    optimizeCss: nextConfig.experimental.optimizeCss,\n    nextConfigOutput: nextConfig.output,\n    nextScriptWorkers: nextConfig.experimental.nextScriptWorkers,\n    largePageDataBytes: nextConfig.experimental.largePageDataBytes,\n    serverActions: nextConfig.experimental.serverActions,\n    serverComponents: enabledDirectories.app,\n    cacheLifeProfiles: nextConfig.experimental.cacheLife,\n    nextFontManifest: require(\n      join(distDir, 'server', `${NEXT_FONT_MANIFEST}.json`)\n    ),\n    images: nextConfig.images,\n    ...(enabledDirectories.app\n      ? {\n          serverActionsManifest,\n        }\n      : {}),\n    strictNextHead: nextConfig.experimental.strictNextHead ?? true,\n    deploymentId: nextConfig.deploymentId,\n    experimental: {\n      clientTraceMetadata: nextConfig.experimental.clientTraceMetadata,\n      expireTime: nextConfig.expireTime,\n      after: nextConfig.experimental.after ?? false,\n      dynamicIO: nextConfig.experimental.dynamicIO ?? false,\n    },\n    reactMaxHeadersLength: nextConfig.reactMaxHeadersLength,\n  }\n\n  const { publicRuntimeConfig } = nextConfig\n\n  if (Object.keys(publicRuntimeConfig).length > 0) {\n    renderOpts.runtimeConfig = publicRuntimeConfig\n  }\n\n  // We need this for server rendering the Link component.\n  ;(globalThis as any).__NEXT_DATA__ = {\n    nextExport: true,\n  }\n\n  const exportPathMap = await span\n    .traceChild('run-export-path-map')\n    .traceAsyncFn(async () => {\n      const exportMap = await nextConfig.exportPathMap(defaultPathMap, {\n        dev: false,\n        dir,\n        outDir,\n        distDir,\n        buildId,\n      })\n      return exportMap\n    })\n\n  // only add missing 404 page when `buildExport` is false\n  if (!options.buildExport) {\n    // only add missing /404 if not specified in `exportPathMap`\n    if (!exportPathMap['/404']) {\n      exportPathMap['/404'] = { page: '/_error' }\n    }\n\n    /**\n     * exports 404.html for backwards compat\n     * E.g. GitHub Pages, GitLab Pages, Cloudflare Pages, Netlify\n     */\n    if (!exportPathMap['/404.html']) {\n      // alias /404.html to /404 to be compatible with custom 404 / _error page\n      exportPathMap['/404.html'] = exportPathMap['/404']\n    }\n  }\n\n  // make sure to prevent duplicates\n  const exportPaths = [\n    ...new Set(\n      Object.keys(exportPathMap).map((path) =>\n        denormalizePagePath(normalizePagePath(path))\n      )\n    ),\n  ]\n\n  const filteredPaths = exportPaths.filter(\n    (route) =>\n      exportPathMap[route]._isAppDir ||\n      // Remove API routes\n      !isAPIRoute(exportPathMap[route].page)\n  )\n\n  if (filteredPaths.length !== exportPaths.length) {\n    hasApiRoutes = true\n  }\n\n  if (filteredPaths.length === 0) {\n    return null\n  }\n\n  if (prerenderManifest && !options.buildExport) {\n    const fallbackEnabledPages = new Set()\n\n    for (const path of Object.keys(exportPathMap)) {\n      const page = exportPathMap[path].page\n      const prerenderInfo = prerenderManifest.dynamicRoutes[page]\n\n      if (prerenderInfo && prerenderInfo.fallback !== false) {\n        fallbackEnabledPages.add(page)\n      }\n    }\n\n    if (fallbackEnabledPages.size > 0) {\n      throw new ExportError(\n        `Found pages with \\`fallback\\` enabled:\\n${[\n          ...fallbackEnabledPages,\n        ].join('\\n')}\\n${SSG_FALLBACK_EXPORT_ERROR}\\n`\n      )\n    }\n  }\n  let hasMiddleware = false\n\n  if (!options.buildExport) {\n    try {\n      const middlewareManifest = require(\n        join(distDir, SERVER_DIRECTORY, MIDDLEWARE_MANIFEST)\n      ) as MiddlewareManifest\n\n      hasMiddleware = Object.keys(middlewareManifest.middleware).length > 0\n    } catch {}\n\n    // Warn if the user defines a path for an API page\n    if (hasApiRoutes || hasMiddleware) {\n      if (nextConfig.output === 'export') {\n        Log.warn(\n          yellow(\n            `Statically exporting a Next.js application via \\`next export\\` disables API routes and middleware.`\n          ) +\n            `\\n` +\n            yellow(\n              `This command is meant for static-only hosts, and is` +\n                ' ' +\n                bold(`not necessary to make your application static.`)\n            ) +\n            `\\n` +\n            yellow(\n              `Pages in your application without server-side data dependencies will be automatically statically exported by \\`next build\\`, including pages powered by \\`getStaticProps\\`.`\n            ) +\n            `\\n` +\n            yellow(\n              `Learn more: https://nextjs.org/docs/messages/api-routes-static-export`\n            )\n        )\n      }\n    }\n  }\n\n  const pagesDataDir = options.buildExport\n    ? outDir\n    : join(outDir, '_next/data', buildId)\n\n  const ampValidations: AmpPageStatus = {}\n\n  const publicDir = join(dir, CLIENT_PUBLIC_FILES_PATH)\n  // Copy public directory\n  if (!options.buildExport && existsSync(publicDir)) {\n    if (!options.silent) {\n      Log.info('Copying \"public\" directory')\n    }\n    await span.traceChild('copy-public-directory').traceAsyncFn(() =>\n      recursiveCopy(publicDir, outDir, {\n        filter(path) {\n          // Exclude paths used by pages\n          return !exportPathMap[path]\n        },\n      })\n    )\n  }\n\n  const failedExportAttemptsByPage: Map<string, boolean> = new Map()\n\n  // Chunk filtered pages into smaller groups, and call the export worker on each group.\n  // We've set a default minimum of 25 pages per chunk to ensure that even setups\n  // with only a few static pages can leverage a shared incremental cache, however this\n  // value can be configured.\n  const minChunkSize =\n    nextConfig.experimental.staticGenerationMinPagesPerWorker ?? 25\n  // Calculate the number of workers needed to ensure each chunk has at least minChunkSize pages\n  const numWorkers = Math.min(\n    options.numWorkers,\n    Math.ceil(filteredPaths.length / minChunkSize)\n  )\n  // Calculate the chunk size based on the number of workers\n  const chunkSize = Math.ceil(filteredPaths.length / numWorkers)\n  const chunks = Array.from({ length: numWorkers }, (_, i) =>\n    filteredPaths.slice(i * chunkSize, (i + 1) * chunkSize)\n  )\n  // Distribute remaining pages\n  const remainingPages = filteredPaths.slice(numWorkers * chunkSize)\n  remainingPages.forEach((page, index) => {\n    chunks[index % chunks.length].push(page)\n  })\n\n  const progress = createProgress(\n    filteredPaths.length,\n    options.statusMessage || 'Exporting'\n  )\n\n  const worker = createStaticWorker(nextConfig, progress)\n\n  const results = (\n    await Promise.all(\n      chunks.map((paths) =>\n        worker.exportPages({\n          paths,\n          exportPathMap,\n          parentSpanId: span.getId(),\n          pagesDataDir,\n          renderOpts,\n          options,\n          dir,\n          distDir,\n          outDir,\n          nextConfig,\n          cacheHandler: nextConfig.cacheHandler,\n          cacheMaxMemorySize: nextConfig.cacheMaxMemorySize,\n          fetchCache: true,\n          fetchCacheKeyPrefix: nextConfig.experimental.fetchCacheKeyPrefix,\n        })\n      )\n    )\n  ).flat()\n\n  let hadValidationError = false\n\n  const collector: ExportAppResult = {\n    byPath: new Map(),\n    byPage: new Map(),\n    ssgNotFoundPaths: new Set(),\n    turborepoAccessTraceResults: new Map(),\n  }\n\n  for (const { result, path, pageKey } of results) {\n    if (!result) continue\n    if ('error' in result) {\n      failedExportAttemptsByPage.set(pageKey, true)\n      continue\n    }\n\n    const { page } = exportPathMap[path]\n\n    if (result.turborepoAccessTraceResult) {\n      collector.turborepoAccessTraceResults?.set(\n        path,\n        TurborepoAccessTraceResult.fromSerialized(\n          result.turborepoAccessTraceResult\n        )\n      )\n    }\n\n    // Capture any amp validations.\n    if (result.ampValidations) {\n      for (const validation of result.ampValidations) {\n        ampValidations[validation.page] = validation.result\n        hadValidationError ||= validation.result.errors.length > 0\n      }\n    }\n\n    if (options.buildExport) {\n      // Update path info by path.\n      const info = collector.byPath.get(path) ?? {}\n      if (typeof result.revalidate !== 'undefined') {\n        info.revalidate = result.revalidate\n      }\n      if (typeof result.metadata !== 'undefined') {\n        info.metadata = result.metadata\n      }\n\n      if (typeof result.hasEmptyPrelude !== 'undefined') {\n        info.hasEmptyPrelude = result.hasEmptyPrelude\n      }\n\n      if (typeof result.hasPostponed !== 'undefined') {\n        info.hasPostponed = result.hasPostponed\n      }\n\n      if (typeof result.fetchMetrics !== 'undefined') {\n        info.fetchMetrics = result.fetchMetrics\n      }\n\n      collector.byPath.set(path, info)\n\n      // Update not found.\n      if (result.ssgNotFound === true) {\n        collector.ssgNotFoundPaths.add(path)\n      }\n\n      // Update durations.\n      const durations = collector.byPage.get(page) ?? {\n        durationsByPath: new Map<string, number>(),\n      }\n      durations.durationsByPath.set(path, result.duration)\n      collector.byPage.set(page, durations)\n    }\n  }\n\n  // Export mode provide static outputs that are not compatible with PPR mode.\n  if (!options.buildExport && nextConfig.experimental.ppr) {\n    // TODO: add message\n    throw new Error('Invariant: PPR cannot be enabled in export mode')\n  }\n\n  // copy prerendered routes to outDir\n  if (!options.buildExport && prerenderManifest) {\n    await Promise.all(\n      Object.keys(prerenderManifest.routes).map(async (route) => {\n        const { srcRoute } = prerenderManifest!.routes[route]\n        const appPageName = mapAppRouteToPage.get(srcRoute || '')\n        const pageName = appPageName || srcRoute || route\n        const isAppPath = Boolean(appPageName)\n        const isAppRouteHandler = appPageName && isAppRouteRoute(appPageName)\n\n        // returning notFound: true from getStaticProps will not\n        // output html/json files during the build\n        if (prerenderManifest!.notFoundRoutes.includes(route)) {\n          return\n        }\n        route = normalizePagePath(route)\n\n        const pagePath = getPagePath(pageName, distDir, undefined, isAppPath)\n        const distPagesDir = join(\n          pagePath,\n          // strip leading / and then recurse number of nested dirs\n          // to place from base folder\n          pageName\n            .slice(1)\n            .split('/')\n            .map(() => '..')\n            .join('/')\n        )\n\n        const orig = join(distPagesDir, route)\n        const handlerSrc = `${orig}.body`\n        const handlerDest = join(outDir, route)\n\n        if (isAppRouteHandler && existsSync(handlerSrc)) {\n          await fs.mkdir(dirname(handlerDest), { recursive: true })\n          await fs.copyFile(handlerSrc, handlerDest)\n          return\n        }\n\n        const htmlDest = join(\n          outDir,\n          `${route}${\n            subFolders && route !== '/index' ? `${sep}index` : ''\n          }.html`\n        )\n        const ampHtmlDest = join(\n          outDir,\n          `${route}.amp${subFolders ? `${sep}index` : ''}.html`\n        )\n        const jsonDest = isAppPath\n          ? join(\n              outDir,\n              `${route}${\n                subFolders && route !== '/index' ? `${sep}index` : ''\n              }.txt`\n            )\n          : join(pagesDataDir, `${route}.json`)\n\n        await fs.mkdir(dirname(htmlDest), { recursive: true })\n        await fs.mkdir(dirname(jsonDest), { recursive: true })\n\n        const htmlSrc = `${orig}.html`\n        const jsonSrc = `${orig}${isAppPath ? RSC_SUFFIX : '.json'}`\n\n        await fs.copyFile(htmlSrc, htmlDest)\n        await fs.copyFile(jsonSrc, jsonDest)\n\n        if (existsSync(`${orig}.amp.html`)) {\n          await fs.mkdir(dirname(ampHtmlDest), { recursive: true })\n          await fs.copyFile(`${orig}.amp.html`, ampHtmlDest)\n        }\n      })\n    )\n  }\n\n  if (Object.keys(ampValidations).length) {\n    console.log(formatAmpMessages(ampValidations))\n  }\n  if (hadValidationError) {\n    throw new ExportError(\n      `AMP Validation caused the export to fail. https://nextjs.org/docs/messages/amp-export-validation`\n    )\n  }\n\n  if (failedExportAttemptsByPage.size > 0) {\n    const failedPages = Array.from(failedExportAttemptsByPage.keys())\n    throw new ExportError(\n      `Export encountered errors on following paths:\\n\\t${failedPages\n        .sort()\n        .join('\\n\\t')}`\n    )\n  }\n\n  await fs.writeFile(\n    join(distDir, EXPORT_DETAIL),\n    formatManifest({\n      version: 1,\n      outDirectory: outDir,\n      success: true,\n    }),\n    'utf8'\n  )\n\n  if (telemetry) {\n    await telemetry.flush()\n  }\n\n  await worker.end()\n\n  return collector\n}\n\nexport default async function exportApp(\n  dir: string,\n  options: ExportAppOptions,\n  span: Span\n): Promise<ExportAppResult | null> {\n  const nextExportSpan = span.traceChild('next-export')\n\n  return nextExportSpan.traceAsyncFn(async () => {\n    return await exportAppImpl(dir, options, nextExportSpan)\n  })\n}\n"], "names": ["ExportError", "exportApp", "Error", "code", "exportAppImpl", "dir", "options", "span", "nextConfig", "resolve", "<PERSON><PERSON><PERSON><PERSON>", "traceFn", "loadEnvConfig", "Log", "enabledDirectories", "traceAsyncFn", "loadConfig", "PHASE_EXPORT", "distDir", "join", "telemetry", "buildExport", "Telemetry", "record", "eventCliSession", "webpackVersion", "cliCommand", "isSrcDir", "has<PERSON>ow<PERSON><PERSON>", "findUp", "cwd", "isCustomServer", "turboFlag", "pagesDir", "appDir", "subFolders", "trailingSlash", "silent", "info", "buildIdFile", "BUILD_ID_FILE", "existsSync", "customRoutes", "filter", "config", "hasNextSupport", "length", "warn", "buildId", "fs", "readFile", "pagesManifest", "pages", "require", "SERVER_DIRECTORY", "PAGES_MANIFEST", "prerenderManifest", "PRERENDER_MANIFEST", "appRoutePathManifest", "APP_PATH_ROUTES_MANIFEST", "err", "isError", "undefined", "excludedPrerenderRoutes", "Set", "Object", "keys", "defaultPathMap", "hasApiRoutes", "page", "isAPIRoute", "dynamicRoutes", "add", "mapAppRouteToPage", "Map", "pageName", "routePath", "entries", "set", "isAppPageRoute", "routes", "_isAppDir", "outDir", "outdir", "rm", "recursive", "force", "mkdir", "writeFile", "EXPORT_DETAIL", "formatManifest", "version", "outDirectory", "success", "recursiveCopy", "CLIENT_STATIC_FILES_PATH", "exportPathMap", "defaultMap", "i18n", "images", "loader", "unoptimized", "isNextImageImported", "EXPORT_MARKER", "then", "text", "JSON", "parse", "catch", "serverActionsManifest", "app", "SERVER_REFERENCE_MANIFEST", "output", "node", "edge", "renderOpts", "previewProps", "preview", "nextExport", "assetPrefix", "replace", "dev", "basePath", "canonicalBase", "amp", "ampSkipValidation", "experimental", "skipValidation", "ampOptimizerConfig", "optimizer", "locales", "locale", "defaultLocale", "domainLocales", "domains", "disableOptimizedLoading", "supportsDynamicResponse", "crossOrigin", "optimizeCss", "nextConfigOutput", "nextScriptWorkers", "largePageDataBytes", "serverActions", "serverComponents", "cacheLifeProfiles", "cacheLife", "nextFontManifest", "NEXT_FONT_MANIFEST", "strictNextHead", "deploymentId", "clientTraceMetadata", "expireTime", "after", "dynamicIO", "reactMaxHeadersLength", "publicRuntimeConfig", "runtimeConfig", "globalThis", "__NEXT_DATA__", "exportMap", "exportPaths", "map", "path", "denormalizePagePath", "normalizePagePath", "filteredPaths", "route", "fallbackEnabledPages", "prerenderInfo", "fallback", "size", "SSG_FALLBACK_EXPORT_ERROR", "hasMiddleware", "middlewareManifest", "MIDDLEWARE_MANIFEST", "middleware", "yellow", "bold", "pagesDataDir", "ampValidations", "publicDir", "CLIENT_PUBLIC_FILES_PATH", "failedExportAttemptsByPage", "minChunkSize", "staticGenerationMinPagesPerWorker", "numWorkers", "Math", "min", "ceil", "chunkSize", "chunks", "Array", "from", "_", "i", "slice", "remainingPages", "for<PERSON>ach", "index", "push", "progress", "createProgress", "statusMessage", "worker", "createStaticWorker", "results", "Promise", "all", "paths", "exportPages", "parentSpanId", "getId", "cache<PERSON><PERSON><PERSON>", "cacheMaxMemorySize", "fetchCache", "fetchCacheKeyPrefix", "flat", "hadValidationError", "collector", "by<PERSON><PERSON>", "byPage", "ssgNotFoundPaths", "turborepoAccessTraceResults", "result", "page<PERSON><PERSON>", "turborepoAccessTraceResult", "TurborepoAccessTraceResult", "fromSerialized", "validation", "errors", "get", "revalidate", "metadata", "hasEmptyPrelude", "hasPostponed", "fetchMetrics", "ssgNotFound", "durations", "durationsByPath", "duration", "ppr", "srcRoute", "appPageName", "isAppPath", "Boolean", "isAppRouteHandler", "isAppRouteRoute", "notFoundRoutes", "includes", "pagePath", "getPagePath", "distPagesDir", "split", "orig", "handlerSrc", "handlerDest", "dirname", "copyFile", "htmlDest", "sep", "ampHtmlDest", "jsonDest", "htmlSrc", "jsonSrc", "RSC_SUFFIX", "console", "log", "formatAmpMessages", "failedPages", "sort", "flush", "end", "nextExportSpan"], "mappings": ";;;;;;;;;;;;;;;IAuDaA,WAAW;eAAXA;;IA0rBb,OAUC;eAV6BC;;;uBA5uB6B;4BAG9B;+DACV;oBACwB;QAEpC;sBAEqC;uBACV;6DAEb;2BACiC;+BACxB;4BAevB;+DACgB;wBAES;wBACD;yBACL;mCACQ;qCACE;qBACN;4BACH;yBACC;iCAGI;gCACD;gEACX;gCACW;sCACY;0BACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGxB,MAAMD,oBAAoBE;;;aAC/BC,OAAO;;AACT;AAEA,eAAeC,cACbC,GAAW,EACXC,OAAmC,EACnCC,IAAU;QAwQOC,iBACIA,8BACCA;IAxQtBH,MAAMI,IAAAA,aAAO,EAACJ;IAEd,4EAA4E;IAC5EE,KAAKG,UAAU,CAAC,eAAeC,OAAO,CAAC,IAAMC,IAAAA,kBAAa,EAACP,KAAK,OAAOQ;IAEvE,MAAM,EAAEC,kBAAkB,EAAE,GAAGR;IAE/B,MAAME,aACJF,QAAQE,UAAU,IACjB,MAAMD,KACJG,UAAU,CAAC,oBACXK,YAAY,CAAC,IAAMC,IAAAA,eAAU,EAACC,wBAAY,EAAEZ;IAEjD,MAAMa,UAAUC,IAAAA,UAAI,EAACd,KAAKG,WAAWU,OAAO;IAC5C,MAAME,YAAYd,QAAQe,WAAW,GAAG,OAAO,IAAIC,kBAAS,CAAC;QAAEJ;IAAQ;IAEvE,IAAIE,WAAW;QACbA,UAAUG,MAAM,CACdC,IAAAA,uBAAe,EAACN,SAASV,YAAY;YACnCiB,gBAAgB;YAChBC,YAAY;YACZC,UAAU;YACVC,YAAY,CAAC,CAAE,MAAMC,IAAAA,eAAM,EAAC,YAAY;gBAAEC,KAAKzB;YAAI;YACnD0B,gBAAgB;YAChBC,WAAW;YACXC,UAAU;YACVC,QAAQ;QACV;IAEJ;IAEA,MAAMC,aAAa3B,WAAW4B,aAAa,IAAI,CAAC9B,QAAQe,WAAW;IAEnE,IAAI,CAACf,QAAQ+B,MAAM,IAAI,CAAC/B,QAAQe,WAAW,EAAE;QAC3CR,KAAIyB,IAAI,CAAC,CAAC,uBAAuB,EAAEpB,QAAQ,CAAC;IAC9C;IAEA,MAAMqB,cAAcpB,IAAAA,UAAI,EAACD,SAASsB,yBAAa;IAE/C,IAAI,CAACC,IAAAA,cAAU,EAACF,cAAc;QAC5B,MAAM,IAAIvC,YACR,CAAC,0CAA0C,EAAEkB,QAAQ,gJAAgJ,CAAC;IAE1M;IAEA,MAAMwB,eAAe;QAAC;QAAY;QAAa;KAAU,CAACC,MAAM,CAC9D,CAACC,SAAW,OAAOpC,UAAU,CAACoC,OAAO,KAAK;IAG5C,IAAI,CAACC,sBAAc,IAAI,CAACvC,QAAQe,WAAW,IAAIqB,aAAaI,MAAM,GAAG,GAAG;QACtEjC,KAAIkC,IAAI,CACN,CAAC,4FAA4F,EAAEL,aAAavB,IAAI,CAC9G,MACA,+EAA+E,CAAC;IAEtF;IAEA,MAAM6B,UAAU,MAAMC,YAAE,CAACC,QAAQ,CAACX,aAAa;IAE/C,MAAMY,gBACJ,CAAC7C,QAAQ8C,KAAK,IACbC,QAAQlC,IAAAA,UAAI,EAACD,SAASoC,4BAAgB,EAAEC,0BAAc;IAEzD,IAAIC;IACJ,IAAI;QACFA,oBAAoBH,QAAQlC,IAAAA,UAAI,EAACD,SAASuC,8BAAkB;IAC9D,EAAE,OAAM,CAAC;IAET,IAAIC;IACJ,IAAI;QACFA,uBAAuBL,QAAQlC,IAAAA,UAAI,EAACD,SAASyC,oCAAwB;IACvE,EAAE,OAAOC,KAAK;QACZ,IACEC,IAAAA,gBAAO,EAACD,QACPA,CAAAA,IAAIzD,IAAI,KAAK,YAAYyD,IAAIzD,IAAI,KAAK,kBAAiB,GACxD;YACA,0DAA0D;YAC1D,oCAAoC;YACpCuD,uBAAuBI;QACzB,OAAO;YACL,2CAA2C;YAC3C,MAAMF;QACR;IACF;IAEA,MAAMG,0BAA0B,IAAIC;IACpC,MAAMZ,QAAQ9C,QAAQ8C,KAAK,IAAIa,OAAOC,IAAI,CAACf;IAC3C,MAAMgB,iBAAgC,CAAC;IAEvC,IAAIC,eAAe;IACnB,KAAK,MAAMC,QAAQjB,MAAO;QACxB,wCAAwC;QACxC,0CAA0C;QAC1C,mCAAmC;QAEnC,IAAIkB,IAAAA,sBAAU,EAACD,OAAO;YACpBD,eAAe;YACf;QACF;QAEA,IAAIC,SAAS,gBAAgBA,SAAS,WAAWA,SAAS,WAAW;YACnE;QACF;QAEA,qEAAqE;QACrE,yEAAyE;QACzE,yEAAyE;QACzE,8CAA8C;QAC9C,IAAIb,qCAAAA,kBAAmBe,aAAa,CAACF,KAAK,EAAE;YAC1CN,wBAAwBS,GAAG,CAACH;YAC5B;QACF;QAEAF,cAAc,CAACE,KAAK,GAAG;YAAEA;QAAK;IAChC;IAEA,MAAMI,oBAAoB,IAAIC;IAC9B,IAAI,CAACpE,QAAQe,WAAW,IAAIqC,sBAAsB;QAChD,KAAK,MAAM,CAACiB,UAAUC,UAAU,IAAIX,OAAOY,OAAO,CAACnB,sBAAuB;YACxEe,kBAAkBK,GAAG,CAACF,WAAWD;YACjC,IACEI,IAAAA,8BAAc,EAACJ,aACf,EAACnB,qCAAAA,kBAAmBwB,MAAM,CAACJ,UAAU,KACrC,EAACpB,qCAAAA,kBAAmBe,aAAa,CAACK,UAAU,GAC5C;gBACAT,cAAc,CAACS,UAAU,GAAG;oBAC1BP,MAAMM;oBACNM,WAAW;gBACb;YACF;QACF;IACF;IAEA,kCAAkC;IAClC,MAAMC,SAAS5E,QAAQ6E,MAAM;IAE7B,IAAID,WAAW/D,IAAAA,UAAI,EAACd,KAAK,WAAW;QAClC,MAAM,IAAIL,YACR,CAAC,wJAAwJ,CAAC;IAE9J;IAEA,IAAIkF,WAAW/D,IAAAA,UAAI,EAACd,KAAK,WAAW;QAClC,MAAM,IAAIL,YACR,CAAC,wJAAwJ,CAAC;IAE9J;IAEA,MAAMiD,YAAE,CAACmC,EAAE,CAACF,QAAQ;QAAEG,WAAW;QAAMC,OAAO;IAAK;IACnD,MAAMrC,YAAE,CAACsC,KAAK,CAACpE,IAAAA,UAAI,EAAC+D,QAAQ,SAASlC,UAAU;QAAEqC,WAAW;IAAK;IAEjE,MAAMpC,YAAE,CAACuC,SAAS,CAChBrE,IAAAA,UAAI,EAACD,SAASuE,yBAAa,GAC3BC,IAAAA,8BAAc,EAAC;QACbC,SAAS;QACTC,cAAcV;QACdW,SAAS;IACX,IACA;IAGF,wBAAwB;IACxB,IAAI,CAACvF,QAAQe,WAAW,IAAIoB,IAAAA,cAAU,EAACtB,IAAAA,UAAI,EAACd,KAAK,YAAY;QAC3D,IAAI,CAACC,QAAQ+B,MAAM,EAAE;YACnBxB,KAAIyB,IAAI,CAAC;QACX;QACA,MAAM/B,KACHG,UAAU,CAAC,yBACXK,YAAY,CAAC,IACZ+E,IAAAA,4BAAa,EAAC3E,IAAAA,UAAI,EAACd,KAAK,WAAWc,IAAAA,UAAI,EAAC+D,QAAQ;IAEtD;IAEA,8BAA8B;IAC9B,IACE,CAAC5E,QAAQe,WAAW,IACpBoB,IAAAA,cAAU,EAACtB,IAAAA,UAAI,EAACD,SAAS6E,oCAAwB,IACjD;QACA,IAAI,CAACzF,QAAQ+B,MAAM,EAAE;YACnBxB,KAAIyB,IAAI,CAAC;QACX;QACA,MAAM/B,KACHG,UAAU,CAAC,8BACXK,YAAY,CAAC,IACZ+E,IAAAA,4BAAa,EACX3E,IAAAA,UAAI,EAACD,SAAS6E,oCAAwB,GACtC5E,IAAAA,UAAI,EAAC+D,QAAQ,SAASa,oCAAwB;IAGtD;IAEA,6CAA6C;IAC7C,IAAI,OAAOvF,WAAWwF,aAAa,KAAK,YAAY;QAClDxF,WAAWwF,aAAa,GAAG,OAAOC;YAChC,OAAOA;QACT;IACF;IAEA,MAAM,EACJC,IAAI,EACJC,QAAQ,EAAEC,SAAS,SAAS,EAAEC,WAAW,EAAE,EAC5C,GAAG7F;IAEJ,IAAI0F,QAAQ,CAAC5F,QAAQe,WAAW,EAAE;QAChC,MAAM,IAAIrB,YACR,CAAC,8IAA8I,CAAC;IAEpJ;IAEA,IAAI,CAACM,QAAQe,WAAW,EAAE;QACxB,MAAM,EAAEiF,mBAAmB,EAAE,GAAG,MAAM/F,KACnCG,UAAU,CAAC,0BACXK,YAAY,CAAC,IACZkC,YAAE,CACCC,QAAQ,CAAC/B,IAAAA,UAAI,EAACD,SAASqF,yBAAa,GAAG,QACvCC,IAAI,CAAC,CAACC,OAASC,KAAKC,KAAK,CAACF,OAC1BG,KAAK,CAAC,IAAO,CAAA,CAAC,CAAA;QAGrB,IACEN,uBACAF,WAAW,aACX,CAACC,eACD,CAACxD,sBAAc,EACf;YACA,MAAM,IAAI7C,YACR,CAAC;;;;8DAIqD,CAAC;QAE3D;IACF;IAEA,IAAI6G;IACJ,IAAI/F,mBAAmBgG,GAAG,EAAE;QAC1BD,wBAAwBxD,QACtBlC,IAAAA,UAAI,EAACD,SAASoC,4BAAgB,EAAEyD,qCAAyB,GAAG;QAE9D,IAAIvG,WAAWwG,MAAM,KAAK,UAAU;YAClC,IACE/C,OAAOC,IAAI,CAAC2C,sBAAsBI,IAAI,EAAEnE,MAAM,GAAG,KACjDmB,OAAOC,IAAI,CAAC2C,sBAAsBK,IAAI,EAAEpE,MAAM,GAAG,GACjD;gBACA,MAAM,IAAI9C,YACR,CAAC,oDAAoD,CAAC;YAE1D;QACF;IACF;IAEA,8BAA8B;IAC9B,MAAMmH,aAAsC;QAC1CC,YAAY,EAAE5D,qCAAAA,kBAAmB6D,OAAO;QACxCrE;QACAsE,YAAY;QACZC,aAAa/G,WAAW+G,WAAW,CAACC,OAAO,CAAC,OAAO;QACnDtG;QACAuG,KAAK;QACLC,UAAUlH,WAAWkH,QAAQ;QAC7BtF,eAAe5B,WAAW4B,aAAa;QACvCuF,eAAenH,EAAAA,kBAAAA,WAAWoH,GAAG,qBAAdpH,gBAAgBmH,aAAa,KAAI;QAChDE,mBAAmBrH,EAAAA,+BAAAA,WAAWsH,YAAY,CAACF,GAAG,qBAA3BpH,6BAA6BuH,cAAc,KAAI;QAClEC,oBAAoBxH,EAAAA,gCAAAA,WAAWsH,YAAY,CAACF,GAAG,qBAA3BpH,8BAA6ByH,SAAS,KAAInE;QAC9DoE,OAAO,EAAEhC,wBAAAA,KAAMgC,OAAO;QACtBC,MAAM,EAAEjC,wBAAAA,KAAMkC,aAAa;QAC3BA,aAAa,EAAElC,wBAAAA,KAAMkC,aAAa;QAClCC,aAAa,EAAEnC,wBAAAA,KAAMoC,OAAO;QAC5BC,yBAAyB/H,WAAWsH,YAAY,CAACS,uBAAuB;QACxE,wDAAwD;QACxDC,yBAAyB;QACzBC,aAAajI,WAAWiI,WAAW;QACnCC,aAAalI,WAAWsH,YAAY,CAACY,WAAW;QAChDC,kBAAkBnI,WAAWwG,MAAM;QACnC4B,mBAAmBpI,WAAWsH,YAAY,CAACc,iBAAiB;QAC5DC,oBAAoBrI,WAAWsH,YAAY,CAACe,kBAAkB;QAC9DC,eAAetI,WAAWsH,YAAY,CAACgB,aAAa;QACpDC,kBAAkBjI,mBAAmBgG,GAAG;QACxCkC,mBAAmBxI,WAAWsH,YAAY,CAACmB,SAAS;QACpDC,kBAAkB7F,QAChBlC,IAAAA,UAAI,EAACD,SAAS,UAAU,CAAC,EAAEiI,8BAAkB,CAAC,KAAK,CAAC;QAEtDhD,QAAQ3F,WAAW2F,MAAM;QACzB,GAAIrF,mBAAmBgG,GAAG,GACtB;YACED;QACF,IACA,CAAC,CAAC;QACNuC,gBAAgB5I,WAAWsH,YAAY,CAACsB,cAAc,IAAI;QAC1DC,cAAc7I,WAAW6I,YAAY;QACrCvB,cAAc;YACZwB,qBAAqB9I,WAAWsH,YAAY,CAACwB,mBAAmB;YAChEC,YAAY/I,WAAW+I,UAAU;YACjCC,OAAOhJ,WAAWsH,YAAY,CAAC0B,KAAK,IAAI;YACxCC,WAAWjJ,WAAWsH,YAAY,CAAC2B,SAAS,IAAI;QAClD;QACAC,uBAAuBlJ,WAAWkJ,qBAAqB;IACzD;IAEA,MAAM,EAAEC,mBAAmB,EAAE,GAAGnJ;IAEhC,IAAIyD,OAAOC,IAAI,CAACyF,qBAAqB7G,MAAM,GAAG,GAAG;QAC/CqE,WAAWyC,aAAa,GAAGD;IAC7B;IAGEE,WAAmBC,aAAa,GAAG;QACnCxC,YAAY;IACd;IAEA,MAAMtB,gBAAgB,MAAMzF,KACzBG,UAAU,CAAC,uBACXK,YAAY,CAAC;QACZ,MAAMgJ,YAAY,MAAMvJ,WAAWwF,aAAa,CAAC7B,gBAAgB;YAC/DsD,KAAK;YACLpH;YACA6E;YACAhE;YACA8B;QACF;QACA,OAAO+G;IACT;IAEF,wDAAwD;IACxD,IAAI,CAACzJ,QAAQe,WAAW,EAAE;QACxB,4DAA4D;QAC5D,IAAI,CAAC2E,aAAa,CAAC,OAAO,EAAE;YAC1BA,aAAa,CAAC,OAAO,GAAG;gBAAE3B,MAAM;YAAU;QAC5C;QAEA;;;KAGC,GACD,IAAI,CAAC2B,aAAa,CAAC,YAAY,EAAE;YAC/B,yEAAyE;YACzEA,aAAa,CAAC,YAAY,GAAGA,aAAa,CAAC,OAAO;QACpD;IACF;IAEA,kCAAkC;IAClC,MAAMgE,cAAc;WACf,IAAIhG,IACLC,OAAOC,IAAI,CAAC8B,eAAeiE,GAAG,CAAC,CAACC,OAC9BC,IAAAA,wCAAmB,EAACC,IAAAA,oCAAiB,EAACF;KAG3C;IAED,MAAMG,gBAAgBL,YAAYrH,MAAM,CACtC,CAAC2H,QACCtE,aAAa,CAACsE,MAAM,CAACrF,SAAS,IAC9B,oBAAoB;QACpB,CAACX,IAAAA,sBAAU,EAAC0B,aAAa,CAACsE,MAAM,CAACjG,IAAI;IAGzC,IAAIgG,cAAcvH,MAAM,KAAKkH,YAAYlH,MAAM,EAAE;QAC/CsB,eAAe;IACjB;IAEA,IAAIiG,cAAcvH,MAAM,KAAK,GAAG;QAC9B,OAAO;IACT;IAEA,IAAIU,qBAAqB,CAAClD,QAAQe,WAAW,EAAE;QAC7C,MAAMkJ,uBAAuB,IAAIvG;QAEjC,KAAK,MAAMkG,QAAQjG,OAAOC,IAAI,CAAC8B,eAAgB;YAC7C,MAAM3B,OAAO2B,aAAa,CAACkE,KAAK,CAAC7F,IAAI;YACrC,MAAMmG,gBAAgBhH,kBAAkBe,aAAa,CAACF,KAAK;YAE3D,IAAImG,iBAAiBA,cAAcC,QAAQ,KAAK,OAAO;gBACrDF,qBAAqB/F,GAAG,CAACH;YAC3B;QACF;QAEA,IAAIkG,qBAAqBG,IAAI,GAAG,GAAG;YACjC,MAAM,IAAI1K,YACR,CAAC,wCAAwC,EAAE;mBACtCuK;aACJ,CAACpJ,IAAI,CAAC,MAAM,EAAE,EAAEwJ,oCAAyB,CAAC,EAAE,CAAC;QAElD;IACF;IACA,IAAIC,gBAAgB;IAEpB,IAAI,CAACtK,QAAQe,WAAW,EAAE;QACxB,IAAI;YACF,MAAMwJ,qBAAqBxH,QACzBlC,IAAAA,UAAI,EAACD,SAASoC,4BAAgB,EAAEwH,+BAAmB;YAGrDF,gBAAgB3G,OAAOC,IAAI,CAAC2G,mBAAmBE,UAAU,EAAEjI,MAAM,GAAG;QACtE,EAAE,OAAM,CAAC;QAET,kDAAkD;QAClD,IAAIsB,gBAAgBwG,eAAe;YACjC,IAAIpK,WAAWwG,MAAM,KAAK,UAAU;gBAClCnG,KAAIkC,IAAI,CACNiI,IAAAA,kBAAM,EACJ,CAAC,kGAAkG,CAAC,IAEpG,CAAC,EAAE,CAAC,GACJA,IAAAA,kBAAM,EACJ,CAAC,mDAAmD,CAAC,GACnD,MACAC,IAAAA,gBAAI,EAAC,CAAC,8CAA8C,CAAC,KAEzD,CAAC,EAAE,CAAC,GACJD,IAAAA,kBAAM,EACJ,CAAC,2KAA2K,CAAC,IAE/K,CAAC,EAAE,CAAC,GACJA,IAAAA,kBAAM,EACJ,CAAC,qEAAqE,CAAC;YAG/E;QACF;IACF;IAEA,MAAME,eAAe5K,QAAQe,WAAW,GACpC6D,SACA/D,IAAAA,UAAI,EAAC+D,QAAQ,cAAclC;IAE/B,MAAMmI,iBAAgC,CAAC;IAEvC,MAAMC,YAAYjK,IAAAA,UAAI,EAACd,KAAKgL,oCAAwB;IACpD,wBAAwB;IACxB,IAAI,CAAC/K,QAAQe,WAAW,IAAIoB,IAAAA,cAAU,EAAC2I,YAAY;QACjD,IAAI,CAAC9K,QAAQ+B,MAAM,EAAE;YACnBxB,KAAIyB,IAAI,CAAC;QACX;QACA,MAAM/B,KAAKG,UAAU,CAAC,yBAAyBK,YAAY,CAAC,IAC1D+E,IAAAA,4BAAa,EAACsF,WAAWlG,QAAQ;gBAC/BvC,QAAOuH,IAAI;oBACT,8BAA8B;oBAC9B,OAAO,CAAClE,aAAa,CAACkE,KAAK;gBAC7B;YACF;IAEJ;IAEA,MAAMoB,6BAAmD,IAAI5G;IAE7D,sFAAsF;IACtF,+EAA+E;IAC/E,qFAAqF;IACrF,2BAA2B;IAC3B,MAAM6G,eACJ/K,WAAWsH,YAAY,CAAC0D,iCAAiC,IAAI;IAC/D,8FAA8F;IAC9F,MAAMC,aAAaC,KAAKC,GAAG,CACzBrL,QAAQmL,UAAU,EAClBC,KAAKE,IAAI,CAACvB,cAAcvH,MAAM,GAAGyI;IAEnC,0DAA0D;IAC1D,MAAMM,YAAYH,KAAKE,IAAI,CAACvB,cAAcvH,MAAM,GAAG2I;IACnD,MAAMK,SAASC,MAAMC,IAAI,CAAC;QAAElJ,QAAQ2I;IAAW,GAAG,CAACQ,GAAGC,IACpD7B,cAAc8B,KAAK,CAACD,IAAIL,WAAW,AAACK,CAAAA,IAAI,CAAA,IAAKL;IAE/C,6BAA6B;IAC7B,MAAMO,iBAAiB/B,cAAc8B,KAAK,CAACV,aAAaI;IACxDO,eAAeC,OAAO,CAAC,CAAChI,MAAMiI;QAC5BR,MAAM,CAACQ,QAAQR,OAAOhJ,MAAM,CAAC,CAACyJ,IAAI,CAAClI;IACrC;IAEA,MAAMmI,WAAWC,IAAAA,wBAAc,EAC7BpC,cAAcvH,MAAM,EACpBxC,QAAQoM,aAAa,IAAI;IAG3B,MAAMC,SAASC,IAAAA,yBAAkB,EAACpM,YAAYgM;IAE9C,MAAMK,UAAU,AACd,CAAA,MAAMC,QAAQC,GAAG,CACfjB,OAAO7B,GAAG,CAAC,CAAC+C,QACVL,OAAOM,WAAW,CAAC;YACjBD;YACAhH;YACAkH,cAAc3M,KAAK4M,KAAK;YACxBjC;YACA/D;YACA7G;YACAD;YACAa;YACAgE;YACA1E;YACA4M,cAAc5M,WAAW4M,YAAY;YACrCC,oBAAoB7M,WAAW6M,kBAAkB;YACjDC,YAAY;YACZC,qBAAqB/M,WAAWsH,YAAY,CAACyF,mBAAmB;QAClE,IAEJ,EACAC,IAAI;IAEN,IAAIC,qBAAqB;IAEzB,MAAMC,YAA6B;QACjCC,QAAQ,IAAIjJ;QACZkJ,QAAQ,IAAIlJ;QACZmJ,kBAAkB,IAAI7J;QACtB8J,6BAA6B,IAAIpJ;IACnC;IAEA,KAAK,MAAM,EAAEqJ,MAAM,EAAE7D,IAAI,EAAE8D,OAAO,EAAE,IAAInB,QAAS;QAC/C,IAAI,CAACkB,QAAQ;QACb,IAAI,WAAWA,QAAQ;YACrBzC,2BAA2BxG,GAAG,CAACkJ,SAAS;YACxC;QACF;QAEA,MAAM,EAAE3J,IAAI,EAAE,GAAG2B,aAAa,CAACkE,KAAK;QAEpC,IAAI6D,OAAOE,0BAA0B,EAAE;gBACrCP;aAAAA,yCAAAA,UAAUI,2BAA2B,qBAArCJ,uCAAuC5I,GAAG,CACxCoF,MACAgE,gDAA0B,CAACC,cAAc,CACvCJ,OAAOE,0BAA0B;QAGvC;QAEA,+BAA+B;QAC/B,IAAIF,OAAO5C,cAAc,EAAE;YACzB,KAAK,MAAMiD,cAAcL,OAAO5C,cAAc,CAAE;gBAC9CA,cAAc,CAACiD,WAAW/J,IAAI,CAAC,GAAG+J,WAAWL,MAAM;gBACnDN,uBAAuBW,WAAWL,MAAM,CAACM,MAAM,CAACvL,MAAM,GAAG;YAC3D;QACF;QAEA,IAAIxC,QAAQe,WAAW,EAAE;YACvB,4BAA4B;YAC5B,MAAMiB,OAAOoL,UAAUC,MAAM,CAACW,GAAG,CAACpE,SAAS,CAAC;YAC5C,IAAI,OAAO6D,OAAOQ,UAAU,KAAK,aAAa;gBAC5CjM,KAAKiM,UAAU,GAAGR,OAAOQ,UAAU;YACrC;YACA,IAAI,OAAOR,OAAOS,QAAQ,KAAK,aAAa;gBAC1ClM,KAAKkM,QAAQ,GAAGT,OAAOS,QAAQ;YACjC;YAEA,IAAI,OAAOT,OAAOU,eAAe,KAAK,aAAa;gBACjDnM,KAAKmM,eAAe,GAAGV,OAAOU,eAAe;YAC/C;YAEA,IAAI,OAAOV,OAAOW,YAAY,KAAK,aAAa;gBAC9CpM,KAAKoM,YAAY,GAAGX,OAAOW,YAAY;YACzC;YAEA,IAAI,OAAOX,OAAOY,YAAY,KAAK,aAAa;gBAC9CrM,KAAKqM,YAAY,GAAGZ,OAAOY,YAAY;YACzC;YAEAjB,UAAUC,MAAM,CAAC7I,GAAG,CAACoF,MAAM5H;YAE3B,oBAAoB;YACpB,IAAIyL,OAAOa,WAAW,KAAK,MAAM;gBAC/BlB,UAAUG,gBAAgB,CAACrJ,GAAG,CAAC0F;YACjC;YAEA,oBAAoB;YACpB,MAAM2E,YAAYnB,UAAUE,MAAM,CAACU,GAAG,CAACjK,SAAS;gBAC9CyK,iBAAiB,IAAIpK;YACvB;YACAmK,UAAUC,eAAe,CAAChK,GAAG,CAACoF,MAAM6D,OAAOgB,QAAQ;YACnDrB,UAAUE,MAAM,CAAC9I,GAAG,CAACT,MAAMwK;QAC7B;IACF;IAEA,4EAA4E;IAC5E,IAAI,CAACvO,QAAQe,WAAW,IAAIb,WAAWsH,YAAY,CAACkH,GAAG,EAAE;QACvD,oBAAoB;QACpB,MAAM,IAAI9O,MAAM;IAClB;IAEA,oCAAoC;IACpC,IAAI,CAACI,QAAQe,WAAW,IAAImC,mBAAmB;QAC7C,MAAMsJ,QAAQC,GAAG,CACf9I,OAAOC,IAAI,CAACV,kBAAkBwB,MAAM,EAAEiF,GAAG,CAAC,OAAOK;YAC/C,MAAM,EAAE2E,QAAQ,EAAE,GAAGzL,kBAAmBwB,MAAM,CAACsF,MAAM;YACrD,MAAM4E,cAAczK,kBAAkB6J,GAAG,CAACW,YAAY;YACtD,MAAMtK,WAAWuK,eAAeD,YAAY3E;YAC5C,MAAM6E,YAAYC,QAAQF;YAC1B,MAAMG,oBAAoBH,eAAeI,IAAAA,gCAAe,EAACJ;YAEzD,wDAAwD;YACxD,0CAA0C;YAC1C,IAAI1L,kBAAmB+L,cAAc,CAACC,QAAQ,CAAClF,QAAQ;gBACrD;YACF;YACAA,QAAQF,IAAAA,oCAAiB,EAACE;YAE1B,MAAMmF,WAAWC,IAAAA,oBAAW,EAAC/K,UAAUzD,SAAS4C,WAAWqL;YAC3D,MAAMQ,eAAexO,IAAAA,UAAI,EACvBsO,UACA,yDAAyD;YACzD,4BAA4B;YAC5B9K,SACGwH,KAAK,CAAC,GACNyD,KAAK,CAAC,KACN3F,GAAG,CAAC,IAAM,MACV9I,IAAI,CAAC;YAGV,MAAM0O,OAAO1O,IAAAA,UAAI,EAACwO,cAAcrF;YAChC,MAAMwF,aAAa,CAAC,EAAED,KAAK,KAAK,CAAC;YACjC,MAAME,cAAc5O,IAAAA,UAAI,EAAC+D,QAAQoF;YAEjC,IAAI+E,qBAAqB5M,IAAAA,cAAU,EAACqN,aAAa;gBAC/C,MAAM7M,YAAE,CAACsC,KAAK,CAACyK,IAAAA,aAAO,EAACD,cAAc;oBAAE1K,WAAW;gBAAK;gBACvD,MAAMpC,YAAE,CAACgN,QAAQ,CAACH,YAAYC;gBAC9B;YACF;YAEA,MAAMG,WAAW/O,IAAAA,UAAI,EACnB+D,QACA,CAAC,EAAEoF,MAAM,EACPnI,cAAcmI,UAAU,WAAW,CAAC,EAAE6F,SAAG,CAAC,KAAK,CAAC,GAAG,GACpD,KAAK,CAAC;YAET,MAAMC,cAAcjP,IAAAA,UAAI,EACtB+D,QACA,CAAC,EAAEoF,MAAM,IAAI,EAAEnI,aAAa,CAAC,EAAEgO,SAAG,CAAC,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC;YAEvD,MAAME,WAAWlB,YACbhO,IAAAA,UAAI,EACF+D,QACA,CAAC,EAAEoF,MAAM,EACPnI,cAAcmI,UAAU,WAAW,CAAC,EAAE6F,SAAG,CAAC,KAAK,CAAC,GAAG,GACpD,IAAI,CAAC,IAERhP,IAAAA,UAAI,EAAC+J,cAAc,CAAC,EAAEZ,MAAM,KAAK,CAAC;YAEtC,MAAMrH,YAAE,CAACsC,KAAK,CAACyK,IAAAA,aAAO,EAACE,WAAW;gBAAE7K,WAAW;YAAK;YACpD,MAAMpC,YAAE,CAACsC,KAAK,CAACyK,IAAAA,aAAO,EAACK,WAAW;gBAAEhL,WAAW;YAAK;YAEpD,MAAMiL,UAAU,CAAC,EAAET,KAAK,KAAK,CAAC;YAC9B,MAAMU,UAAU,CAAC,EAAEV,KAAK,EAAEV,YAAYqB,qBAAU,GAAG,QAAQ,CAAC;YAE5D,MAAMvN,YAAE,CAACgN,QAAQ,CAACK,SAASJ;YAC3B,MAAMjN,YAAE,CAACgN,QAAQ,CAACM,SAASF;YAE3B,IAAI5N,IAAAA,cAAU,EAAC,CAAC,EAAEoN,KAAK,SAAS,CAAC,GAAG;gBAClC,MAAM5M,YAAE,CAACsC,KAAK,CAACyK,IAAAA,aAAO,EAACI,cAAc;oBAAE/K,WAAW;gBAAK;gBACvD,MAAMpC,YAAE,CAACgN,QAAQ,CAAC,CAAC,EAAEJ,KAAK,SAAS,CAAC,EAAEO;YACxC;QACF;IAEJ;IAEA,IAAInM,OAAOC,IAAI,CAACiH,gBAAgBrI,MAAM,EAAE;QACtC2N,QAAQC,GAAG,CAACC,IAAAA,wBAAiB,EAACxF;IAChC;IACA,IAAIsC,oBAAoB;QACtB,MAAM,IAAIzN,YACR,CAAC,gGAAgG,CAAC;IAEtG;IAEA,IAAIsL,2BAA2BZ,IAAI,GAAG,GAAG;QACvC,MAAMkG,cAAc7E,MAAMC,IAAI,CAACV,2BAA2BpH,IAAI;QAC9D,MAAM,IAAIlE,YACR,CAAC,iDAAiD,EAAE4Q,YACjDC,IAAI,GACJ1P,IAAI,CAAC,QAAQ,CAAC;IAErB;IAEA,MAAM8B,YAAE,CAACuC,SAAS,CAChBrE,IAAAA,UAAI,EAACD,SAASuE,yBAAa,GAC3BC,IAAAA,8BAAc,EAAC;QACbC,SAAS;QACTC,cAAcV;QACdW,SAAS;IACX,IACA;IAGF,IAAIzE,WAAW;QACb,MAAMA,UAAU0P,KAAK;IACvB;IAEA,MAAMnE,OAAOoE,GAAG;IAEhB,OAAOrD;AACT;AAEe,eAAezN,UAC5BI,GAAW,EACXC,OAAyB,EACzBC,IAAU;IAEV,MAAMyQ,iBAAiBzQ,KAAKG,UAAU,CAAC;IAEvC,OAAOsQ,eAAejQ,YAAY,CAAC;QACjC,OAAO,MAAMX,cAAcC,KAAKC,SAAS0Q;IAC3C;AACF"}