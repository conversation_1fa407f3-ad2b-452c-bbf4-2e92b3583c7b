{"version": 3, "sources": ["../../../src/export/routes/app-route.ts"], "sourcesContent": ["import type { ExportRouteR<PERSON>ult, FileWriter } from '../types'\nimport type AppRouteRouteModule from '../../server/route-modules/app-route/module'\nimport type { AppRouteRouteHandlerContext } from '../../server/route-modules/app-route/module'\nimport type { IncrementalCache } from '../../server/lib/incremental-cache'\n\nimport {\n  INFINITE_CACHE,\n  NEXT_BODY_SUFFIX,\n  NEXT_CACHE_TAGS_HEADER,\n  NEXT_META_SUFFIX,\n} from '../../lib/constants'\nimport { NodeNextRequest } from '../../server/base-http/node'\nimport {\n  NextRequestAdapter,\n  signalFromNodeResponse,\n} from '../../server/web/spec-extension/adapters/next-request'\nimport { toNodeOutgoingHttpHeaders } from '../../server/web/utils'\nimport type {\n  MockedRequest,\n  MockedResponse,\n} from '../../server/lib/mock-request'\nimport { isDynamicUsageError } from '../helpers/is-dynamic-usage-error'\nimport { hasNextSupport } from '../../server/ci-info'\nimport { isStaticGenEnabled } from '../../server/route-modules/app-route/helpers/is-static-gen-enabled'\nimport type { ExperimentalConfig } from '../../server/config-shared'\nimport { isMetadataRouteFile } from '../../lib/metadata/is-metadata-route'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\nimport type { Params } from '../../server/request/params'\nimport { AfterRunner } from '../../server/after/run-with-after'\n\nexport const enum ExportedAppRouteFiles {\n  BODY = 'BODY',\n  META = 'META',\n}\n\nexport async function exportAppRoute(\n  req: MockedRequest,\n  res: MockedResponse,\n  params: Params | undefined,\n  page: string,\n  module: AppRouteRouteModule,\n  incrementalCache: IncrementalCache | undefined,\n  cacheLifeProfiles:\n    | undefined\n    | {\n        [profile: string]: import('../../server/use-cache/cache-life').CacheLife\n      },\n  htmlFilepath: string,\n  fileWriter: FileWriter,\n  experimental: Required<Pick<ExperimentalConfig, 'after' | 'dynamicIO'>>,\n  buildId: string\n): Promise<ExportRouteResult> {\n  // Ensure that the URL is absolute.\n  req.url = `http://localhost:3000${req.url}`\n\n  // Adapt the request and response to the Next.js request and response.\n  const request = NextRequestAdapter.fromNodeNextRequest(\n    new NodeNextRequest(req),\n    signalFromNodeResponse(res)\n  )\n\n  const afterRunner = new AfterRunner()\n\n  // Create the context for the handler. This contains the params from\n  // the route and the context for the request.\n  const context: AppRouteRouteHandlerContext = {\n    params,\n    prerenderManifest: {\n      version: 4,\n      routes: {},\n      dynamicRoutes: {},\n      preview: {\n        previewModeEncryptionKey: '',\n        previewModeId: '',\n        previewModeSigningKey: '',\n      },\n      notFoundRoutes: [],\n    },\n    renderOpts: {\n      experimental,\n      nextExport: true,\n      supportsDynamicResponse: false,\n      incrementalCache,\n      waitUntil: afterRunner.context.waitUntil,\n      onClose: afterRunner.context.onClose,\n      onAfterTaskError: afterRunner.context.onTaskError,\n      cacheLifeProfiles,\n      buildId,\n    },\n  }\n\n  if (hasNextSupport) {\n    context.renderOpts.isRevalidate = true\n  }\n\n  try {\n    const userland = module.userland\n    // we don't bail from the static optimization for\n    // metadata routes\n    const normalizedPage = normalizeAppPath(page)\n    const isMetadataRoute = isMetadataRouteFile(normalizedPage, [], false)\n\n    if (\n      !isStaticGenEnabled(userland) &&\n      !isMetadataRoute &&\n      // We don't disable static gen when dynamicIO is enabled because we\n      // expect that anything dynamic in the GET handler will make it dynamic\n      // and thus avoid the cache surprises that led to us removing static gen\n      // unless specifically opted into\n      experimental.dynamicIO !== true\n    ) {\n      return { revalidate: 0 }\n    }\n\n    const response = await module.handle(request, context)\n\n    const isValidStatus = response.status < 400 || response.status === 404\n    if (!isValidStatus) {\n      return { revalidate: 0 }\n    }\n\n    const blob = await response.blob()\n\n    // TODO(after): if we abort a prerender because of an error in an after-callback\n    // we should probably communicate that better (and not log the error twice)\n    await afterRunner.executeAfter()\n\n    const revalidate =\n      typeof (context.renderOpts as any).collectedRevalidate === 'undefined' ||\n      (context.renderOpts as any).collectedRevalidate >= INFINITE_CACHE\n        ? false\n        : (context.renderOpts as any).collectedRevalidate\n\n    const headers = toNodeOutgoingHttpHeaders(response.headers)\n    const cacheTags = (context.renderOpts as any).collectedTags\n\n    if (cacheTags) {\n      headers[NEXT_CACHE_TAGS_HEADER] = cacheTags\n    }\n\n    if (!headers['content-type'] && blob.type) {\n      headers['content-type'] = blob.type\n    }\n\n    // Writing response body to a file.\n    const body = Buffer.from(await blob.arrayBuffer())\n    await fileWriter(\n      ExportedAppRouteFiles.BODY,\n      htmlFilepath.replace(/\\.html$/, NEXT_BODY_SUFFIX),\n      body,\n      'utf8'\n    )\n\n    // Write the request metadata to a file.\n    const meta = { status: response.status, headers }\n    await fileWriter(\n      ExportedAppRouteFiles.META,\n      htmlFilepath.replace(/\\.html$/, NEXT_META_SUFFIX),\n      JSON.stringify(meta)\n    )\n\n    return {\n      revalidate: revalidate,\n      metadata: meta,\n    }\n  } catch (err) {\n    if (!isDynamicUsageError(err)) {\n      throw err\n    }\n\n    return { revalidate: 0 }\n  }\n}\n"], "names": ["exportAppRoute", "ExportedAppRouteFiles", "req", "res", "params", "page", "module", "incrementalCache", "cacheLifeProfiles", "htmlFilepath", "fileWriter", "experimental", "buildId", "url", "request", "NextRequestAdapter", "fromNodeNextRequest", "NodeNextRequest", "signalFromNodeResponse", "after<PERSON><PERSON>ner", "After<PERSON><PERSON>ner", "context", "prerenderManifest", "version", "routes", "dynamicRoutes", "preview", "previewModeEncryptionKey", "previewModeId", "previewModeSigningKey", "notFoundRoutes", "renderOpts", "nextExport", "supportsDynamicResponse", "waitUntil", "onClose", "onAfterTaskError", "onTaskError", "hasNextSupport", "isRevalidate", "userland", "normalizedPage", "normalizeAppPath", "isMetadataRoute", "isMetadataRouteFile", "isStaticGenEnabled", "dynamicIO", "revalidate", "response", "handle", "isValidStatus", "status", "blob", "executeAfter", "collectedRevalidate", "INFINITE_CACHE", "headers", "toNodeOutgoingHttpHeaders", "cacheTags", "collectedTags", "NEXT_CACHE_TAGS_HEADER", "type", "body", "<PERSON><PERSON><PERSON>", "from", "arrayBuffer", "replace", "NEXT_BODY_SUFFIX", "meta", "NEXT_META_SUFFIX", "JSON", "stringify", "metadata", "err", "isDynamicUsageError"], "mappings": ";;;;;;;;;;;;;;;;;;IAmCsBA,cAAc;eAAdA;;;2BAzBf;sBACyB;6BAIzB;uBACmC;qCAKN;wBACL;oCACI;iCAEC;0BACH;8BAEL;;UAEVC;;;GAAAA,0BAAAA;AAKX,eAAeD,eACpBE,GAAkB,EAClBC,GAAmB,EACnBC,MAA0B,EAC1BC,IAAY,EACZC,OAA2B,EAC3BC,gBAA8C,EAC9CC,iBAIK,EACLC,YAAoB,EACpBC,UAAsB,EACtBC,YAAuE,EACvEC,OAAe;IAEf,mCAAmC;IACnCV,IAAIW,GAAG,GAAG,CAAC,qBAAqB,EAAEX,IAAIW,GAAG,CAAC,CAAC;IAE3C,sEAAsE;IACtE,MAAMC,UAAUC,+BAAkB,CAACC,mBAAmB,CACpD,IAAIC,qBAAe,CAACf,MACpBgB,IAAAA,mCAAsB,EAACf;IAGzB,MAAMgB,cAAc,IAAIC,yBAAW;IAEnC,oEAAoE;IACpE,6CAA6C;IAC7C,MAAMC,UAAuC;QAC3CjB;QACAkB,mBAAmB;YACjBC,SAAS;YACTC,QAAQ,CAAC;YACTC,eAAe,CAAC;YAChBC,SAAS;gBACPC,0BAA0B;gBAC1BC,eAAe;gBACfC,uBAAuB;YACzB;YACAC,gBAAgB,EAAE;QACpB;QACAC,YAAY;YACVpB;YACAqB,YAAY;YACZC,yBAAyB;YACzB1B;YACA2B,WAAWf,YAAYE,OAAO,CAACa,SAAS;YACxCC,SAAShB,YAAYE,OAAO,CAACc,OAAO;YACpCC,kBAAkBjB,YAAYE,OAAO,CAACgB,WAAW;YACjD7B;YACAI;QACF;IACF;IAEA,IAAI0B,sBAAc,EAAE;QAClBjB,QAAQU,UAAU,CAACQ,YAAY,GAAG;IACpC;IAEA,IAAI;QACF,MAAMC,WAAWlC,QAAOkC,QAAQ;QAChC,iDAAiD;QACjD,kBAAkB;QAClB,MAAMC,iBAAiBC,IAAAA,0BAAgB,EAACrC;QACxC,MAAMsC,kBAAkBC,IAAAA,oCAAmB,EAACH,gBAAgB,EAAE,EAAE;QAEhE,IACE,CAACI,IAAAA,sCAAkB,EAACL,aACpB,CAACG,mBACD,mEAAmE;QACnE,uEAAuE;QACvE,wEAAwE;QACxE,iCAAiC;QACjChC,aAAamC,SAAS,KAAK,MAC3B;YACA,OAAO;gBAAEC,YAAY;YAAE;QACzB;QAEA,MAAMC,WAAW,MAAM1C,QAAO2C,MAAM,CAACnC,SAASO;QAE9C,MAAM6B,gBAAgBF,SAASG,MAAM,GAAG,OAAOH,SAASG,MAAM,KAAK;QACnE,IAAI,CAACD,eAAe;YAClB,OAAO;gBAAEH,YAAY;YAAE;QACzB;QAEA,MAAMK,OAAO,MAAMJ,SAASI,IAAI;QAEhC,gFAAgF;QAChF,2EAA2E;QAC3E,MAAMjC,YAAYkC,YAAY;QAE9B,MAAMN,aACJ,OAAO,AAAC1B,QAAQU,UAAU,CAASuB,mBAAmB,KAAK,eAC3D,AAACjC,QAAQU,UAAU,CAASuB,mBAAmB,IAAIC,yBAAc,GAC7D,QACA,AAAClC,QAAQU,UAAU,CAASuB,mBAAmB;QAErD,MAAME,UAAUC,IAAAA,gCAAyB,EAACT,SAASQ,OAAO;QAC1D,MAAME,YAAY,AAACrC,QAAQU,UAAU,CAAS4B,aAAa;QAE3D,IAAID,WAAW;YACbF,OAAO,CAACI,iCAAsB,CAAC,GAAGF;QACpC;QAEA,IAAI,CAACF,OAAO,CAAC,eAAe,IAAIJ,KAAKS,IAAI,EAAE;YACzCL,OAAO,CAAC,eAAe,GAAGJ,KAAKS,IAAI;QACrC;QAEA,mCAAmC;QACnC,MAAMC,OAAOC,OAAOC,IAAI,CAAC,MAAMZ,KAAKa,WAAW;QAC/C,MAAMvD,mBAEJD,aAAayD,OAAO,CAAC,WAAWC,2BAAgB,GAChDL,MACA;QAGF,wCAAwC;QACxC,MAAMM,OAAO;YAAEjB,QAAQH,SAASG,MAAM;YAAEK;QAAQ;QAChD,MAAM9C,mBAEJD,aAAayD,OAAO,CAAC,WAAWG,2BAAgB,GAChDC,KAAKC,SAAS,CAACH;QAGjB,OAAO;YACLrB,YAAYA;YACZyB,UAAUJ;QACZ;IACF,EAAE,OAAOK,KAAK;QACZ,IAAI,CAACC,IAAAA,wCAAmB,EAACD,MAAM;YAC7B,MAAMA;QACR;QAEA,OAAO;YAAE1B,YAAY;QAAE;IACzB;AACF"}