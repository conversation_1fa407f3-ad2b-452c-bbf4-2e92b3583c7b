{"version": 3, "sources": ["../../src/export/worker.ts"], "sourcesContent": ["import type {\n  ExportPagesInput,\n  ExportPageInput,\n  ExportPageResult,\n  ExportRouteResult,\n  ExportedPageFile,\n  FileWriter,\n  WorkerRenderOpts,\n  ExportPagesResult,\n} from './types'\n\nimport '../server/node-environment'\n\nprocess.env.NEXT_IS_EXPORT_WORKER = 'true'\n\nimport { extname, join, dirname, sep } from 'path'\nimport fs from 'fs/promises'\nimport { loadComponents } from '../server/load-components'\nimport { isDynamicRoute } from '../shared/lib/router/utils/is-dynamic'\nimport { normalizePagePath } from '../shared/lib/page-path/normalize-page-path'\nimport { normalizeLocalePath } from '../shared/lib/i18n/normalize-locale-path'\nimport { trace } from '../trace'\nimport { setHttpClientAndAgentOptions } from '../server/setup-http-agent-env'\nimport isError from '../lib/is-error'\nimport { addRequestMeta } from '../server/request-meta'\nimport { normalizeAppPath } from '../shared/lib/router/utils/app-paths'\n\nimport { createRequestResponseMocks } from '../server/lib/mock-request'\nimport { isAppRouteRoute } from '../lib/is-app-route-route'\nimport { hasNextSupport } from '../server/ci-info'\nimport { exportAppRoute } from './routes/app-route'\nimport { exportAppPage, prospectiveRenderAppPage } from './routes/app-page'\nimport { exportPagesPage } from './routes/pages'\nimport { getParams } from './helpers/get-params'\nimport { createIncrementalCache } from './helpers/create-incremental-cache'\nimport { isPostpone } from '../server/lib/router-utils/is-postpone'\nimport { isDynamicUsageError } from './helpers/is-dynamic-usage-error'\nimport { isBailoutToCSRError } from '../shared/lib/lazy-dynamic/bailout-to-csr'\nimport {\n  turborepoTraceAccess,\n  TurborepoAccessTraceResult,\n} from '../build/turborepo-access-trace'\nimport type { Params } from '../server/request/params'\nimport {\n  getFallbackRouteParams,\n  type FallbackRouteParams,\n} from '../server/request/fallback-params'\nimport { needsExperimentalReact } from '../lib/needs-experimental-react'\nimport { runWithCacheScope } from '../server/async-storage/cache-scope.external'\nimport type { AppRouteRouteModule } from '../server/route-modules/app-route/module.compiled'\n\nconst envConfig = require('../shared/lib/runtime-config.external')\n\n;(globalThis as any).__NEXT_DATA__ = {\n  nextExport: true,\n}\n\nclass TimeoutError extends Error {\n  code = 'NEXT_EXPORT_TIMEOUT_ERROR'\n}\n\nclass ExportPageError extends Error {\n  code = 'NEXT_EXPORT_PAGE_ERROR'\n}\n\nasync function exportPageImpl(\n  input: ExportPageInput,\n  fileWriter: FileWriter\n): Promise<ExportRouteResult | undefined> {\n  const {\n    path,\n    pathMap,\n    distDir,\n    pagesDataDir,\n    buildExport = false,\n    serverRuntimeConfig,\n    subFolders = false,\n    optimizeCss,\n    disableOptimizedLoading,\n    debugOutput = false,\n    enableExperimentalReact,\n    ampValidatorPath,\n    trailingSlash,\n  } = input\n\n  if (enableExperimentalReact) {\n    process.env.__NEXT_EXPERIMENTAL_REACT = 'true'\n  }\n\n  const {\n    page,\n\n    // The parameters that are currently unknown.\n    _fallbackRouteParams = [],\n\n    // Check if this is an `app/` page.\n    _isAppDir: isAppDir = false,\n\n    // Check if this should error when dynamic usage is detected.\n    _isDynamicError: isDynamicError = false,\n\n    // If this page supports partial prerendering, then we need to pass that to\n    // the renderOpts.\n    _isRoutePPREnabled: isRoutePPREnabled,\n\n    // If this is a prospective render, we don't actually want to persist the\n    // result, we just want to use it to error the build if there's a problem.\n    _isProspectiveRender: isProspectiveRender = false,\n\n    // Pull the original query out.\n    query: originalQuery = {},\n  } = pathMap\n\n  const fallbackRouteParams: FallbackRouteParams | null =\n    getFallbackRouteParams(_fallbackRouteParams)\n\n  let query = { ...originalQuery }\n  const pathname = normalizeAppPath(page)\n  const isDynamic = isDynamicRoute(page)\n  const outDir = isAppDir ? join(distDir, 'server/app') : input.outDir\n\n  const filePath = normalizePagePath(path)\n  const ampPath = `${filePath}.amp`\n  let renderAmpPath = ampPath\n\n  let updatedPath = query.__nextSsgPath || path\n  delete query.__nextSsgPath\n\n  let locale = query.__nextLocale || input.renderOpts.locale\n  delete query.__nextLocale\n\n  if (input.renderOpts.locale) {\n    const localePathResult = normalizeLocalePath(path, input.renderOpts.locales)\n\n    if (localePathResult.detectedLocale) {\n      updatedPath = localePathResult.pathname\n      locale = localePathResult.detectedLocale\n\n      if (locale === input.renderOpts.defaultLocale) {\n        renderAmpPath = `${normalizePagePath(updatedPath)}.amp`\n      }\n    }\n  }\n\n  // We need to show a warning if they try to provide query values\n  // for an auto-exported page since they won't be available\n  const hasOrigQueryValues = Object.keys(originalQuery).length > 0\n\n  // Check if the page is a specified dynamic route\n  const { pathname: nonLocalizedPath } = normalizeLocalePath(\n    path,\n    input.renderOpts.locales\n  )\n\n  let params: Params | undefined\n\n  if (isDynamic && page !== nonLocalizedPath) {\n    const normalizedPage = isAppDir ? normalizeAppPath(page) : page\n\n    params = getParams(normalizedPage, updatedPath)\n    if (params) {\n      query = {\n        ...query,\n        ...params,\n      }\n    }\n  }\n\n  const { req, res } = createRequestResponseMocks({ url: updatedPath })\n\n  // If this is a status code page, then set the response code.\n  for (const statusCode of [404, 500]) {\n    if (\n      [\n        `/${statusCode}`,\n        `/${statusCode}.html`,\n        `/${statusCode}/index.html`,\n      ].some((p) => p === updatedPath || `/${locale}${p}` === updatedPath)\n    ) {\n      res.statusCode = statusCode\n    }\n  }\n\n  // Ensure that the URL has a trailing slash if it's configured.\n  if (trailingSlash && !req.url?.endsWith('/')) {\n    req.url += '/'\n  }\n\n  if (\n    locale &&\n    buildExport &&\n    input.renderOpts.domainLocales &&\n    input.renderOpts.domainLocales.some(\n      (dl) => dl.defaultLocale === locale || dl.locales?.includes(locale || '')\n    )\n  ) {\n    addRequestMeta(req, 'isLocaleDomain', true)\n  }\n\n  envConfig.setConfig({\n    serverRuntimeConfig,\n    publicRuntimeConfig: input.renderOpts.runtimeConfig,\n  })\n\n  const getHtmlFilename = (p: string) =>\n    subFolders ? `${p}${sep}index.html` : `${p}.html`\n\n  let htmlFilename = getHtmlFilename(filePath)\n\n  // dynamic routes can provide invalid extensions e.g. /blog/[...slug] returns an\n  // extension of `.slug]`\n  const pageExt = isDynamic || isAppDir ? '' : extname(page)\n  const pathExt = isDynamic || isAppDir ? '' : extname(path)\n\n  // force output 404.html for backwards compat\n  if (path === '/404.html') {\n    htmlFilename = path\n  }\n  // Make sure page isn't a folder with a dot in the name e.g. `v1.2`\n  else if (pageExt !== pathExt && pathExt !== '') {\n    const isBuiltinPaths = ['/500', '/404'].some(\n      (p) => p === path || p === path + '.html'\n    )\n    // If the ssg path has .html extension, and it's not builtin paths, use it directly\n    // Otherwise, use that as the filename instead\n    const isHtmlExtPath = !isBuiltinPaths && path.endsWith('.html')\n    htmlFilename = isHtmlExtPath ? getHtmlFilename(path) : path\n  } else if (path === '/') {\n    // If the path is the root, just use index.html\n    htmlFilename = 'index.html'\n  }\n\n  const baseDir = join(outDir, dirname(htmlFilename))\n  let htmlFilepath = join(outDir, htmlFilename)\n\n  await fs.mkdir(baseDir, { recursive: true })\n\n  const components = await loadComponents({\n    distDir,\n    page,\n    isAppPath: isAppDir,\n  })\n\n  // Handle App Routes.\n  if (isAppDir && isAppRouteRoute(page)) {\n    return exportAppRoute(\n      req,\n      res,\n      params,\n      page,\n      components.routeModule as AppRouteRouteModule,\n      input.renderOpts.incrementalCache,\n      input.renderOpts.cacheLifeProfiles,\n      htmlFilepath,\n      fileWriter,\n      input.renderOpts.experimental,\n      input.renderOpts.buildId\n    )\n  }\n\n  const renderOpts: WorkerRenderOpts = {\n    ...components,\n    ...input.renderOpts,\n    ampPath: renderAmpPath,\n    params,\n    optimizeCss,\n    disableOptimizedLoading,\n    locale,\n    supportsDynamicResponse: false,\n    experimental: {\n      ...input.renderOpts.experimental,\n      isRoutePPREnabled,\n    },\n  }\n\n  if (hasNextSupport) {\n    renderOpts.isRevalidate = true\n  }\n\n  // Handle App Pages\n  if (isAppDir) {\n    // If this is a prospective render, don't return any metrics or revalidate\n    // timings as we aren't persisting this render (it was only to error).\n    if (isProspectiveRender) {\n      return prospectiveRenderAppPage(\n        req,\n        res,\n        page,\n        pathname,\n        query,\n        fallbackRouteParams,\n        renderOpts\n      )\n    }\n\n    return exportAppPage(\n      req,\n      res,\n      page,\n      path,\n      pathname,\n      query,\n      fallbackRouteParams,\n      renderOpts,\n      htmlFilepath,\n      debugOutput,\n      isDynamicError,\n      fileWriter\n    )\n  }\n\n  return exportPagesPage(\n    req,\n    res,\n    path,\n    page,\n    query,\n    htmlFilepath,\n    htmlFilename,\n    ampPath,\n    subFolders,\n    outDir,\n    ampValidatorPath,\n    pagesDataDir,\n    buildExport,\n    isDynamic,\n    hasOrigQueryValues,\n    renderOpts,\n    components,\n    fileWriter\n  )\n}\n\nexport async function exportPages(\n  input: ExportPagesInput\n): Promise<ExportPagesResult> {\n  const {\n    exportPathMap,\n    paths,\n    dir,\n    distDir,\n    outDir,\n    cacheHandler,\n    cacheMaxMemorySize,\n    fetchCacheKeyPrefix,\n    pagesDataDir,\n    renderOpts,\n    nextConfig,\n    options,\n  } = input\n\n  // If the fetch cache was enabled, we need to create an incremental\n  // cache instance for this page.\n  const incrementalCache = await createIncrementalCache({\n    cacheHandler,\n    cacheMaxMemorySize,\n    fetchCacheKeyPrefix,\n    distDir,\n    dir,\n    dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n    // skip writing to disk in minimal mode for now, pending some\n    // changes to better support it\n    flushToDisk: !hasNextSupport,\n    cacheHandlers: nextConfig.experimental.cacheHandlers,\n  })\n\n  renderOpts.incrementalCache = incrementalCache\n\n  const maxConcurrency =\n    nextConfig.experimental.staticGenerationMaxConcurrency ?? 8\n  const results: ExportPagesResult = []\n\n  const exportPageWithRetry = async (path: string, maxAttempts: number) => {\n    const pathMap = exportPathMap[path]\n    const { page } = exportPathMap[path]\n    const pageKey = page !== path ? `${page}: ${path}` : path\n    let attempt = 0\n    let result\n\n    while (attempt < maxAttempts) {\n      try {\n        result = await Promise.race<ExportPageResult | undefined>([\n          exportPage({\n            path,\n            pathMap,\n            distDir,\n            outDir,\n            pagesDataDir,\n            renderOpts,\n            ampValidatorPath:\n              nextConfig.experimental.amp?.validator || undefined,\n            trailingSlash: nextConfig.trailingSlash,\n            serverRuntimeConfig: nextConfig.serverRuntimeConfig,\n            subFolders: nextConfig.trailingSlash && !options.buildExport,\n            buildExport: options.buildExport,\n            optimizeCss: nextConfig.experimental.optimizeCss,\n            disableOptimizedLoading:\n              nextConfig.experimental.disableOptimizedLoading,\n            parentSpanId: input.parentSpanId,\n            httpAgentOptions: nextConfig.httpAgentOptions,\n            debugOutput: options.debugOutput,\n            enableExperimentalReact: needsExperimentalReact(nextConfig),\n          }),\n          // If exporting the page takes longer than the timeout, reject the promise.\n          new Promise((_, reject) => {\n            setTimeout(() => {\n              reject(new TimeoutError())\n            }, nextConfig.staticPageGenerationTimeout * 1000)\n          }),\n        ])\n\n        // If there was an error in the export, throw it immediately. In the catch block, we might retry the export,\n        // or immediately fail the build, depending on user configuration. We might also continue on and attempt other pages.\n        if (result && 'error' in result) {\n          throw new ExportPageError()\n        }\n\n        // If the export succeeds, break out of the retry loop\n        break\n      } catch (err) {\n        // The only error that should be caught here is an ExportError, as `exportPage` doesn't throw and instead returns an object with an `error` property.\n        // This is an overly cautious check to ensure that we don't accidentally catch an unexpected error.\n        if (!(err instanceof ExportPageError || err instanceof TimeoutError)) {\n          throw err\n        }\n\n        if (err instanceof TimeoutError) {\n          // If the export times out, we will restart the worker up to 3 times.\n          maxAttempts = 3\n        }\n\n        // We've reached the maximum number of attempts\n        if (attempt >= maxAttempts - 1) {\n          // Log a message if we've reached the maximum number of attempts.\n          // We only care to do this if maxAttempts was configured.\n          if (maxAttempts > 0) {\n            console.info(\n              `Failed to build ${pageKey} after ${maxAttempts} attempts.`\n            )\n          }\n          // If prerenderEarlyExit is enabled, we'll exit the build immediately.\n          if (nextConfig.experimental.prerenderEarlyExit) {\n            throw new ExportPageError(\n              `Export encountered an error on ${pageKey}, exiting the build.`\n            )\n          } else {\n            // Otherwise, this is a no-op. The build will continue, and a summary of failed pages will be displayed at the end.\n          }\n        } else {\n          // Otherwise, we have more attempts to make. Wait before retrying\n          if (err instanceof TimeoutError) {\n            console.info(\n              `Failed to build ${pageKey} (attempt ${attempt + 1} of ${maxAttempts}) because it took more than ${nextConfig.staticPageGenerationTimeout} seconds. Retrying again shortly.`\n            )\n          } else {\n            console.info(\n              `Failed to build ${pageKey} (attempt ${attempt + 1} of ${maxAttempts}). Retrying again shortly.`\n            )\n          }\n          await new Promise((r) => setTimeout(r, Math.random() * 500))\n        }\n      }\n\n      attempt++\n    }\n\n    return { result, path, pageKey }\n  }\n  // for each build worker we share one dynamic IO cache scope\n  // this is only leveraged if the flag is enabled\n  const dynamicIOCacheScope = new Map()\n\n  await runWithCacheScope({ cache: dynamicIOCacheScope }, async () => {\n    for (let i = 0; i < paths.length; i += maxConcurrency) {\n      const subset = paths.slice(i, i + maxConcurrency)\n\n      const subsetResults = await Promise.all(\n        subset.map((path) =>\n          exportPageWithRetry(\n            path,\n            nextConfig.experimental.staticGenerationRetryCount ?? 1\n          )\n        )\n      )\n\n      results.push(...subsetResults)\n    }\n  })\n\n  return results\n}\n\nasync function exportPage(\n  input: ExportPageInput\n): Promise<ExportPageResult | undefined> {\n  trace('export-page', input.parentSpanId).setAttribute('path', input.path)\n\n  // Configure the http agent.\n  setHttpClientAndAgentOptions({\n    httpAgentOptions: input.httpAgentOptions,\n  })\n\n  const files: ExportedPageFile[] = []\n  const baseFileWriter: FileWriter = async (\n    type,\n    path,\n    content,\n    encodingOptions = 'utf-8'\n  ) => {\n    await fs.mkdir(dirname(path), { recursive: true })\n    await fs.writeFile(path, content, encodingOptions)\n    files.push({ type, path })\n  }\n\n  const exportPageSpan = trace('export-page-worker', input.parentSpanId)\n\n  const start = Date.now()\n\n  const turborepoAccessTraceResult = new TurborepoAccessTraceResult()\n\n  // Export the page.\n  let result: ExportRouteResult | undefined\n  try {\n    result = await exportPageSpan.traceAsyncFn(() =>\n      turborepoTraceAccess(\n        () => exportPageImpl(input, baseFileWriter),\n        turborepoAccessTraceResult\n      )\n    )\n\n    // If there was no result, then we can exit early.\n    if (!result) return\n\n    // If there was an error, then we can exit early.\n    if ('error' in result) {\n      return { error: result.error, duration: Date.now() - start, files: [] }\n    }\n  } catch (err) {\n    console.error(\n      `\\nError occurred prerendering page \"${input.path}\". Read more: https://nextjs.org/docs/messages/prerender-error\\n`\n    )\n\n    if (!isBailoutToCSRError(err)) {\n      console.error(isError(err) && err.stack ? err.stack : err)\n    }\n\n    return { error: true, duration: Date.now() - start, files: [] }\n  }\n\n  // Notify the parent process that we processed a page (used by the progress activity indicator)\n  process.send?.([3, { type: 'activity' }])\n\n  // Otherwise we can return the result.\n  return {\n    duration: Date.now() - start,\n    files,\n    ampValidations: result.ampValidations,\n    revalidate: result.revalidate,\n    metadata: result.metadata,\n    ssgNotFound: result.ssgNotFound,\n    hasEmptyPrelude: result.hasEmptyPrelude,\n    hasPostponed: result.hasPostponed,\n    turborepoAccessTraceResult: turborepoAccessTraceResult.serialize(),\n    fetchMetrics: result.fetchMetrics,\n  }\n}\n\nprocess.on('unhandledRejection', (err: unknown) => {\n  // if it's a postpone error, it'll be handled later\n  // when the postponed promise is actually awaited.\n  if (isPostpone(err)) {\n    return\n  }\n\n  // we don't want to log these errors\n  if (isDynamicUsageError(err)) {\n    return\n  }\n\n  console.error(err)\n})\n\nprocess.on('rejectionHandled', () => {\n  // It is ok to await a Promise late in Next.js as it allows for better\n  // prefetching patterns to avoid waterfalls. We ignore logging these.\n  // We should've already errored in anyway unhandledRejection.\n})\n\nconst FATAL_UNHANDLED_NEXT_API_EXIT_CODE = 78\n\nprocess.on('uncaughtException', (err) => {\n  if (isDynamicUsageError(err)) {\n    console.error(\n      'A Next.js API that uses exceptions to signal framework behavior was uncaught. This suggests improper usage of a Next.js API. The original error is printed below and the build will now exit.'\n    )\n    console.error(err)\n    process.exit(FATAL_UNHANDLED_NEXT_API_EXIT_CODE)\n  } else {\n    console.error(err)\n  }\n})\n"], "names": ["exportPages", "process", "env", "NEXT_IS_EXPORT_WORKER", "envConfig", "require", "globalThis", "__NEXT_DATA__", "nextExport", "TimeoutError", "Error", "code", "ExportPageError", "exportPageImpl", "input", "fileWriter", "req", "path", "pathMap", "distDir", "pagesDataDir", "buildExport", "serverRuntimeConfig", "subFolders", "optimizeCss", "disableOptimizedLoading", "debugOutput", "enableExperimentalReact", "ampValidator<PERSON>ath", "trailingSlash", "__NEXT_EXPERIMENTAL_REACT", "page", "_fallbackRouteParams", "_isAppDir", "isAppDir", "_isDynamicError", "isDynamicError", "_isRoutePPREnabled", "isRoutePPREnabled", "_isProspectiveRender", "isProspectiveRender", "query", "originalQuery", "fallbackRouteParams", "getFallbackRouteParams", "pathname", "normalizeAppPath", "isDynamic", "isDynamicRoute", "outDir", "join", "filePath", "normalizePagePath", "ampPath", "renderAmpPath", "updatedPath", "__nextSsgPath", "locale", "__next<PERSON><PERSON><PERSON>", "renderOpts", "localePathResult", "normalizeLocalePath", "locales", "detectedLocale", "defaultLocale", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "keys", "length", "nonLocalizedPath", "params", "normalizedPage", "getParams", "res", "createRequestResponseMocks", "url", "statusCode", "some", "p", "endsWith", "domainLocales", "dl", "includes", "addRequestMeta", "setConfig", "publicRuntimeConfig", "runtimeConfig", "getHtmlFilename", "sep", "htmlFilename", "pageExt", "extname", "pathExt", "isBuiltinPaths", "isHtmlExtPath", "baseDir", "dirname", "htmlFilepath", "fs", "mkdir", "recursive", "components", "loadComponents", "isAppPath", "isAppRouteRoute", "exportAppRoute", "routeModule", "incrementalCache", "cacheLifeProfiles", "experimental", "buildId", "supportsDynamicResponse", "hasNextSupport", "isRevalidate", "prospectiveRenderAppPage", "exportAppPage", "exportPagesPage", "exportPathMap", "paths", "dir", "cache<PERSON><PERSON><PERSON>", "cacheMaxMemorySize", "fetchCacheKeyPrefix", "nextConfig", "options", "createIncrementalCache", "dynamicIO", "Boolean", "flushToDisk", "cacheHandlers", "maxConcurrency", "staticGenerationMaxConcurrency", "results", "exportPageWithRetry", "maxAttempts", "page<PERSON><PERSON>", "attempt", "result", "Promise", "race", "exportPage", "amp", "validator", "undefined", "parentSpanId", "httpAgentOptions", "needsExperimentalReact", "_", "reject", "setTimeout", "staticPageGenerationTimeout", "err", "console", "info", "prerenderEarlyExit", "r", "Math", "random", "dynamicIOCacheScope", "Map", "runWithCacheScope", "cache", "i", "subset", "slice", "subsetResults", "all", "map", "staticGenerationRetryCount", "push", "trace", "setAttribute", "setHttpClientAndAgentOptions", "files", "baseFileWriter", "type", "content", "encodingOptions", "writeFile", "exportPageSpan", "start", "Date", "now", "turborepoAccessTraceResult", "TurborepoAccessTraceResult", "traceAsyncFn", "turborepoTraceAccess", "error", "duration", "isBailoutToCSRError", "isError", "stack", "send", "ampValidations", "revalidate", "metadata", "ssgNotFound", "hasEmptyPrelude", "hasPostponed", "serialize", "fetchMetrics", "on", "isPostpone", "isDynamicUsageError", "FATAL_UNHANDLED_NEXT_API_EXIT_CODE", "exit"], "mappings": ";;;;+BA6UsBA;;;eAAAA;;;QAlUf;sBAIqC;iEAC7B;gCACgB;2BACA;mCACG;qCACE;uBACd;mCACuB;gEACzB;6BACW;0BACE;6BAEU;iCACX;wBACD;0BACA;yBACyB;uBACxB;2BACN;wCACa;4BACZ;qCACS;8BACA;sCAI7B;gCAKA;wCACgC;oCACL;;;;;;AAnClCC,QAAQC,GAAG,CAACC,qBAAqB,GAAG;AAsCpC,MAAMC,YAAYC,QAAQ;AAExBC,WAAmBC,aAAa,GAAG;IACnCC,YAAY;AACd;AAEA,MAAMC,qBAAqBC;;;aACzBC,OAAO;;AACT;AAEA,MAAMC,wBAAwBF;;;aAC5BC,OAAO;;AACT;AAEA,eAAeE,eACbC,KAAsB,EACtBC,UAAsB;QAqHAC;IAnHtB,MAAM,EACJC,IAAI,EACJC,OAAO,EACPC,OAAO,EACPC,YAAY,EACZC,cAAc,KAAK,EACnBC,mBAAmB,EACnBC,aAAa,KAAK,EAClBC,WAAW,EACXC,uBAAuB,EACvBC,cAAc,KAAK,EACnBC,uBAAuB,EACvBC,gBAAgB,EAChBC,aAAa,EACd,GAAGf;IAEJ,IAAIa,yBAAyB;QAC3B1B,QAAQC,GAAG,CAAC4B,yBAAyB,GAAG;IAC1C;IAEA,MAAM,EACJC,IAAI,EAEJ,6CAA6C;IAC7CC,uBAAuB,EAAE,EAEzB,mCAAmC;IACnCC,WAAWC,WAAW,KAAK,EAE3B,6DAA6D;IAC7DC,iBAAiBC,iBAAiB,KAAK,EAEvC,2EAA2E;IAC3E,kBAAkB;IAClBC,oBAAoBC,iBAAiB,EAErC,yEAAyE;IACzE,0EAA0E;IAC1EC,sBAAsBC,sBAAsB,KAAK,EAEjD,+BAA+B;IAC/BC,OAAOC,gBAAgB,CAAC,CAAC,EAC1B,GAAGxB;IAEJ,MAAMyB,sBACJC,IAAAA,sCAAsB,EAACZ;IAEzB,IAAIS,QAAQ;QAAE,GAAGC,aAAa;IAAC;IAC/B,MAAMG,WAAWC,IAAAA,0BAAgB,EAACf;IAClC,MAAMgB,YAAYC,IAAAA,yBAAc,EAACjB;IACjC,MAAMkB,SAASf,WAAWgB,IAAAA,UAAI,EAAC/B,SAAS,gBAAgBL,MAAMmC,MAAM;IAEpE,MAAME,WAAWC,IAAAA,oCAAiB,EAACnC;IACnC,MAAMoC,UAAU,CAAC,EAAEF,SAAS,IAAI,CAAC;IACjC,IAAIG,gBAAgBD;IAEpB,IAAIE,cAAcd,MAAMe,aAAa,IAAIvC;IACzC,OAAOwB,MAAMe,aAAa;IAE1B,IAAIC,SAAShB,MAAMiB,YAAY,IAAI5C,MAAM6C,UAAU,CAACF,MAAM;IAC1D,OAAOhB,MAAMiB,YAAY;IAEzB,IAAI5C,MAAM6C,UAAU,CAACF,MAAM,EAAE;QAC3B,MAAMG,mBAAmBC,IAAAA,wCAAmB,EAAC5C,MAAMH,MAAM6C,UAAU,CAACG,OAAO;QAE3E,IAAIF,iBAAiBG,cAAc,EAAE;YACnCR,cAAcK,iBAAiBf,QAAQ;YACvCY,SAASG,iBAAiBG,cAAc;YAExC,IAAIN,WAAW3C,MAAM6C,UAAU,CAACK,aAAa,EAAE;gBAC7CV,gBAAgB,CAAC,EAAEF,IAAAA,oCAAiB,EAACG,aAAa,IAAI,CAAC;YACzD;QACF;IACF;IAEA,gEAAgE;IAChE,0DAA0D;IAC1D,MAAMU,qBAAqBC,OAAOC,IAAI,CAACzB,eAAe0B,MAAM,GAAG;IAE/D,iDAAiD;IACjD,MAAM,EAAEvB,UAAUwB,gBAAgB,EAAE,GAAGR,IAAAA,wCAAmB,EACxD5C,MACAH,MAAM6C,UAAU,CAACG,OAAO;IAG1B,IAAIQ;IAEJ,IAAIvB,aAAahB,SAASsC,kBAAkB;QAC1C,MAAME,iBAAiBrC,WAAWY,IAAAA,0BAAgB,EAACf,QAAQA;QAE3DuC,SAASE,IAAAA,oBAAS,EAACD,gBAAgBhB;QACnC,IAAIe,QAAQ;YACV7B,QAAQ;gBACN,GAAGA,KAAK;gBACR,GAAG6B,MAAM;YACX;QACF;IACF;IAEA,MAAM,EAAEtD,GAAG,EAAEyD,GAAG,EAAE,GAAGC,IAAAA,uCAA0B,EAAC;QAAEC,KAAKpB;IAAY;IAEnE,6DAA6D;IAC7D,KAAK,MAAMqB,cAAc;QAAC;QAAK;KAAI,CAAE;QACnC,IACE;YACE,CAAC,CAAC,EAAEA,WAAW,CAAC;YAChB,CAAC,CAAC,EAAEA,WAAW,KAAK,CAAC;YACrB,CAAC,CAAC,EAAEA,WAAW,WAAW,CAAC;SAC5B,CAACC,IAAI,CAAC,CAACC,IAAMA,MAAMvB,eAAe,CAAC,CAAC,EAAEE,OAAO,EAAEqB,EAAE,CAAC,KAAKvB,cACxD;YACAkB,IAAIG,UAAU,GAAGA;QACnB;IACF;IAEA,+DAA+D;IAC/D,IAAI/C,iBAAiB,GAACb,WAAAA,IAAI2D,GAAG,qBAAP3D,SAAS+D,QAAQ,CAAC,OAAM;QAC5C/D,IAAI2D,GAAG,IAAI;IACb;IAEA,IACElB,UACApC,eACAP,MAAM6C,UAAU,CAACqB,aAAa,IAC9BlE,MAAM6C,UAAU,CAACqB,aAAa,CAACH,IAAI,CACjC,CAACI;YAAsCA;eAA/BA,GAAGjB,aAAa,KAAKP,YAAUwB,cAAAA,GAAGnB,OAAO,qBAAVmB,YAAYC,QAAQ,CAACzB,UAAU;QAExE;QACA0B,IAAAA,2BAAc,EAACnE,KAAK,kBAAkB;IACxC;IAEAZ,UAAUgF,SAAS,CAAC;QAClB9D;QACA+D,qBAAqBvE,MAAM6C,UAAU,CAAC2B,aAAa;IACrD;IAEA,MAAMC,kBAAkB,CAACT,IACvBvD,aAAa,CAAC,EAAEuD,EAAE,EAAEU,SAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EAAEV,EAAE,KAAK,CAAC;IAEnD,IAAIW,eAAeF,gBAAgBpC;IAEnC,gFAAgF;IAChF,wBAAwB;IACxB,MAAMuC,UAAU3C,aAAab,WAAW,KAAKyD,IAAAA,aAAO,EAAC5D;IACrD,MAAM6D,UAAU7C,aAAab,WAAW,KAAKyD,IAAAA,aAAO,EAAC1E;IAErD,6CAA6C;IAC7C,IAAIA,SAAS,aAAa;QACxBwE,eAAexE;IACjB,OAEK,IAAIyE,YAAYE,WAAWA,YAAY,IAAI;QAC9C,MAAMC,iBAAiB;YAAC;YAAQ;SAAO,CAAChB,IAAI,CAC1C,CAACC,IAAMA,MAAM7D,QAAQ6D,MAAM7D,OAAO;QAEpC,mFAAmF;QACnF,8CAA8C;QAC9C,MAAM6E,gBAAgB,CAACD,kBAAkB5E,KAAK8D,QAAQ,CAAC;QACvDU,eAAeK,gBAAgBP,gBAAgBtE,QAAQA;IACzD,OAAO,IAAIA,SAAS,KAAK;QACvB,+CAA+C;QAC/CwE,eAAe;IACjB;IAEA,MAAMM,UAAU7C,IAAAA,UAAI,EAACD,QAAQ+C,IAAAA,aAAO,EAACP;IACrC,IAAIQ,eAAe/C,IAAAA,UAAI,EAACD,QAAQwC;IAEhC,MAAMS,iBAAE,CAACC,KAAK,CAACJ,SAAS;QAAEK,WAAW;IAAK;IAE1C,MAAMC,aAAa,MAAMC,IAAAA,8BAAc,EAAC;QACtCnF;QACAY;QACAwE,WAAWrE;IACb;IAEA,qBAAqB;IACrB,IAAIA,YAAYsE,IAAAA,gCAAe,EAACzE,OAAO;QACrC,OAAO0E,IAAAA,wBAAc,EACnBzF,KACAyD,KACAH,QACAvC,MACAsE,WAAWK,WAAW,EACtB5F,MAAM6C,UAAU,CAACgD,gBAAgB,EACjC7F,MAAM6C,UAAU,CAACiD,iBAAiB,EAClCX,cACAlF,YACAD,MAAM6C,UAAU,CAACkD,YAAY,EAC7B/F,MAAM6C,UAAU,CAACmD,OAAO;IAE5B;IAEA,MAAMnD,aAA+B;QACnC,GAAG0C,UAAU;QACb,GAAGvF,MAAM6C,UAAU;QACnBN,SAASC;QACTgB;QACA9C;QACAC;QACAgC;QACAsD,yBAAyB;QACzBF,cAAc;YACZ,GAAG/F,MAAM6C,UAAU,CAACkD,YAAY;YAChCvE;QACF;IACF;IAEA,IAAI0E,sBAAc,EAAE;QAClBrD,WAAWsD,YAAY,GAAG;IAC5B;IAEA,mBAAmB;IACnB,IAAI/E,UAAU;QACZ,0EAA0E;QAC1E,sEAAsE;QACtE,IAAIM,qBAAqB;YACvB,OAAO0E,IAAAA,iCAAwB,EAC7BlG,KACAyD,KACA1C,MACAc,UACAJ,OACAE,qBACAgB;QAEJ;QAEA,OAAOwD,IAAAA,sBAAa,EAClBnG,KACAyD,KACA1C,MACAd,MACA4B,UACAJ,OACAE,qBACAgB,YACAsC,cACAvE,aACAU,gBACArB;IAEJ;IAEA,OAAOqG,IAAAA,sBAAe,EACpBpG,KACAyD,KACAxD,MACAc,MACAU,OACAwD,cACAR,cACApC,SACA9B,YACA0B,QACArB,kBACAR,cACAC,aACA0B,WACAkB,oBACAN,YACA0C,YACAtF;AAEJ;AAEO,eAAef,YACpBc,KAAuB;IAEvB,MAAM,EACJuG,aAAa,EACbC,KAAK,EACLC,GAAG,EACHpG,OAAO,EACP8B,MAAM,EACNuE,YAAY,EACZC,kBAAkB,EAClBC,mBAAmB,EACnBtG,YAAY,EACZuC,UAAU,EACVgE,UAAU,EACVC,OAAO,EACR,GAAG9G;IAEJ,mEAAmE;IACnE,gCAAgC;IAChC,MAAM6F,mBAAmB,MAAMkB,IAAAA,8CAAsB,EAAC;QACpDL;QACAC;QACAC;QACAvG;QACAoG;QACAO,WAAWC,QAAQJ,WAAWd,YAAY,CAACiB,SAAS;QACpD,6DAA6D;QAC7D,+BAA+B;QAC/BE,aAAa,CAAChB,sBAAc;QAC5BiB,eAAeN,WAAWd,YAAY,CAACoB,aAAa;IACtD;IAEAtE,WAAWgD,gBAAgB,GAAGA;IAE9B,MAAMuB,iBACJP,WAAWd,YAAY,CAACsB,8BAA8B,IAAI;IAC5D,MAAMC,UAA6B,EAAE;IAErC,MAAMC,sBAAsB,OAAOpH,MAAcqH;QAC/C,MAAMpH,UAAUmG,aAAa,CAACpG,KAAK;QACnC,MAAM,EAAEc,IAAI,EAAE,GAAGsF,aAAa,CAACpG,KAAK;QACpC,MAAMsH,UAAUxG,SAASd,OAAO,CAAC,EAAEc,KAAK,EAAE,EAAEd,KAAK,CAAC,GAAGA;QACrD,IAAIuH,UAAU;QACd,IAAIC;QAEJ,MAAOD,UAAUF,YAAa;YAC5B,IAAI;oBAUIX;gBATNc,SAAS,MAAMC,QAAQC,IAAI,CAA+B;oBACxDC,WAAW;wBACT3H;wBACAC;wBACAC;wBACA8B;wBACA7B;wBACAuC;wBACA/B,kBACE+F,EAAAA,+BAAAA,WAAWd,YAAY,CAACgC,GAAG,qBAA3BlB,6BAA6BmB,SAAS,KAAIC;wBAC5ClH,eAAe8F,WAAW9F,aAAa;wBACvCP,qBAAqBqG,WAAWrG,mBAAmB;wBACnDC,YAAYoG,WAAW9F,aAAa,IAAI,CAAC+F,QAAQvG,WAAW;wBAC5DA,aAAauG,QAAQvG,WAAW;wBAChCG,aAAamG,WAAWd,YAAY,CAACrF,WAAW;wBAChDC,yBACEkG,WAAWd,YAAY,CAACpF,uBAAuB;wBACjDuH,cAAclI,MAAMkI,YAAY;wBAChCC,kBAAkBtB,WAAWsB,gBAAgB;wBAC7CvH,aAAakG,QAAQlG,WAAW;wBAChCC,yBAAyBuH,IAAAA,8CAAsB,EAACvB;oBAClD;oBACA,2EAA2E;oBAC3E,IAAIe,QAAQ,CAACS,GAAGC;wBACdC,WAAW;4BACTD,OAAO,IAAI3I;wBACb,GAAGkH,WAAW2B,2BAA2B,GAAG;oBAC9C;iBACD;gBAED,4GAA4G;gBAC5G,qHAAqH;gBACrH,IAAIb,UAAU,WAAWA,QAAQ;oBAC/B,MAAM,IAAI7H;gBACZ;gBAGA;YACF,EAAE,OAAO2I,KAAK;gBACZ,qJAAqJ;gBACrJ,mGAAmG;gBACnG,IAAI,CAAEA,CAAAA,eAAe3I,mBAAmB2I,eAAe9I,YAAW,GAAI;oBACpE,MAAM8I;gBACR;gBAEA,IAAIA,eAAe9I,cAAc;oBAC/B,qEAAqE;oBACrE6H,cAAc;gBAChB;gBAEA,+CAA+C;gBAC/C,IAAIE,WAAWF,cAAc,GAAG;oBAC9B,iEAAiE;oBACjE,yDAAyD;oBACzD,IAAIA,cAAc,GAAG;wBACnBkB,QAAQC,IAAI,CACV,CAAC,gBAAgB,EAAElB,QAAQ,OAAO,EAAED,YAAY,UAAU,CAAC;oBAE/D;oBACA,sEAAsE;oBACtE,IAAIX,WAAWd,YAAY,CAAC6C,kBAAkB,EAAE;wBAC9C,MAAM,IAAI9I,gBACR,CAAC,+BAA+B,EAAE2H,QAAQ,oBAAoB,CAAC;oBAEnE,OAAO;oBACL,mHAAmH;oBACrH;gBACF,OAAO;oBACL,iEAAiE;oBACjE,IAAIgB,eAAe9I,cAAc;wBAC/B+I,QAAQC,IAAI,CACV,CAAC,gBAAgB,EAAElB,QAAQ,UAAU,EAAEC,UAAU,EAAE,IAAI,EAAEF,YAAY,4BAA4B,EAAEX,WAAW2B,2BAA2B,CAAC,iCAAiC,CAAC;oBAEhL,OAAO;wBACLE,QAAQC,IAAI,CACV,CAAC,gBAAgB,EAAElB,QAAQ,UAAU,EAAEC,UAAU,EAAE,IAAI,EAAEF,YAAY,0BAA0B,CAAC;oBAEpG;oBACA,MAAM,IAAII,QAAQ,CAACiB,IAAMN,WAAWM,GAAGC,KAAKC,MAAM,KAAK;gBACzD;YACF;YAEArB;QACF;QAEA,OAAO;YAAEC;YAAQxH;YAAMsH;QAAQ;IACjC;IACA,4DAA4D;IAC5D,gDAAgD;IAChD,MAAMuB,sBAAsB,IAAIC;IAEhC,MAAMC,IAAAA,qCAAiB,EAAC;QAAEC,OAAOH;IAAoB,GAAG;QACtD,IAAK,IAAII,IAAI,GAAGA,IAAI5C,MAAMlD,MAAM,EAAE8F,KAAKhC,eAAgB;YACrD,MAAMiC,SAAS7C,MAAM8C,KAAK,CAACF,GAAGA,IAAIhC;YAElC,MAAMmC,gBAAgB,MAAM3B,QAAQ4B,GAAG,CACrCH,OAAOI,GAAG,CAAC,CAACtJ,OACVoH,oBACEpH,MACA0G,WAAWd,YAAY,CAAC2D,0BAA0B,IAAI;YAK5DpC,QAAQqC,IAAI,IAAIJ;QAClB;IACF;IAEA,OAAOjC;AACT;AAEA,eAAeQ,WACb9H,KAAsB;IAEtB4J,IAAAA,YAAK,EAAC,eAAe5J,MAAMkI,YAAY,EAAE2B,YAAY,CAAC,QAAQ7J,MAAMG,IAAI;IAExE,4BAA4B;IAC5B2J,IAAAA,+CAA4B,EAAC;QAC3B3B,kBAAkBnI,MAAMmI,gBAAgB;IAC1C;IAEA,MAAM4B,QAA4B,EAAE;IACpC,MAAMC,iBAA6B,OACjCC,MACA9J,MACA+J,SACAC,kBAAkB,OAAO;QAEzB,MAAM/E,iBAAE,CAACC,KAAK,CAACH,IAAAA,aAAO,EAAC/E,OAAO;YAAEmF,WAAW;QAAK;QAChD,MAAMF,iBAAE,CAACgF,SAAS,CAACjK,MAAM+J,SAASC;QAClCJ,MAAMJ,IAAI,CAAC;YAAEM;YAAM9J;QAAK;IAC1B;IAEA,MAAMkK,iBAAiBT,IAAAA,YAAK,EAAC,sBAAsB5J,MAAMkI,YAAY;IAErE,MAAMoC,QAAQC,KAAKC,GAAG;IAEtB,MAAMC,6BAA6B,IAAIC,gDAA0B;IAEjE,mBAAmB;IACnB,IAAI/C;IACJ,IAAI;QACFA,SAAS,MAAM0C,eAAeM,YAAY,CAAC,IACzCC,IAAAA,0CAAoB,EAClB,IAAM7K,eAAeC,OAAOgK,iBAC5BS;QAIJ,kDAAkD;QAClD,IAAI,CAAC9C,QAAQ;QAEb,iDAAiD;QACjD,IAAI,WAAWA,QAAQ;YACrB,OAAO;gBAAEkD,OAAOlD,OAAOkD,KAAK;gBAAEC,UAAUP,KAAKC,GAAG,KAAKF;gBAAOP,OAAO,EAAE;YAAC;QACxE;IACF,EAAE,OAAOtB,KAAK;QACZC,QAAQmC,KAAK,CACX,CAAC,oCAAoC,EAAE7K,MAAMG,IAAI,CAAC,gEAAgE,CAAC;QAGrH,IAAI,CAAC4K,IAAAA,iCAAmB,EAACtC,MAAM;YAC7BC,QAAQmC,KAAK,CAACG,IAAAA,gBAAO,EAACvC,QAAQA,IAAIwC,KAAK,GAAGxC,IAAIwC,KAAK,GAAGxC;QACxD;QAEA,OAAO;YAAEoC,OAAO;YAAMC,UAAUP,KAAKC,GAAG,KAAKF;YAAOP,OAAO,EAAE;QAAC;IAChE;IAEA,+FAA+F;IAC/F5K,QAAQ+L,IAAI,oBAAZ/L,QAAQ+L,IAAI,MAAZ/L,SAAe;QAAC;QAAG;YAAE8K,MAAM;QAAW;KAAE;IAExC,sCAAsC;IACtC,OAAO;QACLa,UAAUP,KAAKC,GAAG,KAAKF;QACvBP;QACAoB,gBAAgBxD,OAAOwD,cAAc;QACrCC,YAAYzD,OAAOyD,UAAU;QAC7BC,UAAU1D,OAAO0D,QAAQ;QACzBC,aAAa3D,OAAO2D,WAAW;QAC/BC,iBAAiB5D,OAAO4D,eAAe;QACvCC,cAAc7D,OAAO6D,YAAY;QACjCf,4BAA4BA,2BAA2BgB,SAAS;QAChEC,cAAc/D,OAAO+D,YAAY;IACnC;AACF;AAEAvM,QAAQwM,EAAE,CAAC,sBAAsB,CAAClD;IAChC,mDAAmD;IACnD,kDAAkD;IAClD,IAAImD,IAAAA,sBAAU,EAACnD,MAAM;QACnB;IACF;IAEA,oCAAoC;IACpC,IAAIoD,IAAAA,wCAAmB,EAACpD,MAAM;QAC5B;IACF;IAEAC,QAAQmC,KAAK,CAACpC;AAChB;AAEAtJ,QAAQwM,EAAE,CAAC,oBAAoB;AAC7B,sEAAsE;AACtE,qEAAqE;AACrE,6DAA6D;AAC/D;AAEA,MAAMG,qCAAqC;AAE3C3M,QAAQwM,EAAE,CAAC,qBAAqB,CAAClD;IAC/B,IAAIoD,IAAAA,wCAAmB,EAACpD,MAAM;QAC5BC,QAAQmC,KAAK,CACX;QAEFnC,QAAQmC,KAAK,CAACpC;QACdtJ,QAAQ4M,IAAI,CAACD;IACf,OAAO;QACLpD,QAAQmC,KAAK,CAACpC;IAChB;AACF"}