{"version": 3, "sources": ["../../../src/trace/report/to-json.ts"], "sourcesContent": ["import { randomBytes } from 'crypto'\nimport { traceGlobals } from '../shared'\nimport fs from 'fs'\nimport path from 'path'\nimport { PHASE_DEVELOPMENT_SERVER } from '../../shared/lib/constants'\nimport type { TraceEvent } from '../types'\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nconst localEndpoint = {\n  serviceName: 'nextjs',\n  ipv4: '127.0.0.1',\n  port: 9411,\n}\n\ntype Event = TraceEvent & {\n  localEndpoint?: typeof localEndpoint\n}\n\n// Batch events as zipkin allows for multiple events to be sent in one go\nexport function batcher(reportEvents: (evts: Event[]) => Promise<void>) {\n  const events: Event[] = []\n  // Promise queue to ensure events are always sent on flushAll\n  const queue = new Set()\n  return {\n    flushAll: async () => {\n      await Promise.all(queue)\n      if (events.length > 0) {\n        await reportEvents(events)\n        events.length = 0\n      }\n    },\n    report: (event: Event) => {\n      events.push(event)\n\n      if (events.length > 100) {\n        const evts = events.slice()\n        events.length = 0\n        const report = reportEvents(evts)\n        queue.add(report)\n        report.then(() => queue.delete(report))\n      }\n    },\n  }\n}\n\nlet writeStream: RotatingWriteStream\nlet traceId: string\nlet batch: ReturnType<typeof batcher> | undefined\n\nconst writeStreamOptions = {\n  flags: 'a',\n  encoding: 'utf8' as const,\n}\nclass RotatingWriteStream {\n  file: string\n  writeStream!: fs.WriteStream\n  size: number\n  sizeLimit: number\n  private rotatePromise: Promise<void> | undefined\n  private drainPromise: Promise<void> | undefined\n  constructor(file: string, sizeLimit: number) {\n    this.file = file\n    this.size = 0\n    this.sizeLimit = sizeLimit\n    this.createWriteStream()\n  }\n  private createWriteStream() {\n    this.writeStream = fs.createWriteStream(this.file, writeStreamOptions)\n  }\n  // Recreate the file\n  private async rotate() {\n    await this.end()\n    try {\n      fs.unlinkSync(this.file)\n    } catch (err: any) {\n      // It's fine if the file does not exist yet\n      if (err.code !== 'ENOENT') {\n        throw err\n      }\n    }\n    this.size = 0\n    this.createWriteStream()\n    this.rotatePromise = undefined\n  }\n  async write(data: string): Promise<void> {\n    if (this.rotatePromise) await this.rotatePromise\n\n    this.size += data.length\n    if (this.size > this.sizeLimit) {\n      await (this.rotatePromise = this.rotate())\n    }\n\n    if (!this.writeStream.write(data, 'utf8')) {\n      if (this.drainPromise === undefined) {\n        this.drainPromise = new Promise<void>((resolve, _reject) => {\n          this.writeStream.once('drain', () => {\n            this.drainPromise = undefined\n            resolve()\n          })\n        })\n      }\n      await this.drainPromise\n    }\n  }\n\n  end(): Promise<void> {\n    return new Promise((resolve) => {\n      this.writeStream.end(resolve)\n    })\n  }\n}\n\nconst reportToLocalHost = (event: TraceEvent) => {\n  const distDir = traceGlobals.get('distDir')\n  const phase = traceGlobals.get('phase')\n  if (!distDir || !phase) {\n    return\n  }\n\n  if (!traceId) {\n    traceId = process.env.TRACE_ID || randomBytes(8).toString('hex')\n  }\n\n  if (!batch) {\n    batch = batcher(async (events: Event[]) => {\n      if (!writeStream) {\n        await fs.promises.mkdir(distDir, { recursive: true })\n        const file = path.join(distDir, 'trace')\n        writeStream = new RotatingWriteStream(\n          file,\n          // Development is limited to 50MB, production is unlimited\n          phase === PHASE_DEVELOPMENT_SERVER ? 52428800 : Infinity\n        )\n      }\n      const eventsJson = JSON.stringify(events)\n      try {\n        await writeStream.write(eventsJson + '\\n')\n      } catch (err) {\n        console.log(err)\n      }\n    })\n  }\n\n  batch.report({\n    ...event,\n    traceId,\n  })\n}\n\nexport default {\n  flushAll: (opts?: { end: boolean }) =>\n    batch\n      ? batch.flushAll().then(() => {\n          const phase = traceGlobals.get('phase')\n          // Only end writeStream when manually flushing in production\n          if (opts?.end || phase !== PHASE_DEVELOPMENT_SERVER) {\n            return writeStream.end()\n          }\n        })\n      : undefined,\n  report: reportToLocalHost,\n}\n"], "names": ["batcher", "localEndpoint", "serviceName", "ipv4", "port", "reportEvents", "events", "queue", "Set", "flushAll", "Promise", "all", "length", "report", "event", "push", "evts", "slice", "add", "then", "delete", "writeStream", "traceId", "batch", "writeStreamOptions", "flags", "encoding", "RotatingWriteStream", "constructor", "file", "sizeLimit", "size", "createWriteStream", "fs", "rotate", "end", "unlinkSync", "err", "code", "rotatePromise", "undefined", "write", "data", "drainPromise", "resolve", "_reject", "once", "reportToLocalHost", "distDir", "traceGlobals", "get", "phase", "process", "env", "TRACE_ID", "randomBytes", "toString", "promises", "mkdir", "recursive", "path", "join", "PHASE_DEVELOPMENT_SERVER", "Infinity", "eventsJson", "JSON", "stringify", "console", "log", "opts"], "mappings": ";;;;;;;;;;;;;;;IAmBgBA,OAAO;eAAPA;;IAkIhB,OAYC;eAZD;;;wBArJ4B;wBACC;2DACd;6DACE;2BACwB;;;;;;AAGzC,6DAA6D;AAC7D,MAAMC,gBAAgB;IACpBC,aAAa;IACbC,MAAM;IACNC,MAAM;AACR;AAOO,SAASJ,QAAQK,YAA8C;IACpE,MAAMC,SAAkB,EAAE;IAC1B,6DAA6D;IAC7D,MAAMC,QAAQ,IAAIC;IAClB,OAAO;QACLC,UAAU;YACR,MAAMC,QAAQC,GAAG,CAACJ;YAClB,IAAID,OAAOM,MAAM,GAAG,GAAG;gBACrB,MAAMP,aAAaC;gBACnBA,OAAOM,MAAM,GAAG;YAClB;QACF;QACAC,QAAQ,CAACC;YACPR,OAAOS,IAAI,CAACD;YAEZ,IAAIR,OAAOM,MAAM,GAAG,KAAK;gBACvB,MAAMI,OAAOV,OAAOW,KAAK;gBACzBX,OAAOM,MAAM,GAAG;gBAChB,MAAMC,SAASR,aAAaW;gBAC5BT,MAAMW,GAAG,CAACL;gBACVA,OAAOM,IAAI,CAAC,IAAMZ,MAAMa,MAAM,CAACP;YACjC;QACF;IACF;AACF;AAEA,IAAIQ;AACJ,IAAIC;AACJ,IAAIC;AAEJ,MAAMC,qBAAqB;IACzBC,OAAO;IACPC,UAAU;AACZ;AACA,MAAMC;IAOJC,YAAYC,IAAY,EAAEC,SAAiB,CAAE;QAC3C,IAAI,CAACD,IAAI,GAAGA;QACZ,IAAI,CAACE,IAAI,GAAG;QACZ,IAAI,CAACD,SAAS,GAAGA;QACjB,IAAI,CAACE,iBAAiB;IACxB;IACQA,oBAAoB;QAC1B,IAAI,CAACX,WAAW,GAAGY,WAAE,CAACD,iBAAiB,CAAC,IAAI,CAACH,IAAI,EAAEL;IACrD;IACA,oBAAoB;IACpB,MAAcU,SAAS;QACrB,MAAM,IAAI,CAACC,GAAG;QACd,IAAI;YACFF,WAAE,CAACG,UAAU,CAAC,IAAI,CAACP,IAAI;QACzB,EAAE,OAAOQ,KAAU;YACjB,2CAA2C;YAC3C,IAAIA,IAAIC,IAAI,KAAK,UAAU;gBACzB,MAAMD;YACR;QACF;QACA,IAAI,CAACN,IAAI,GAAG;QACZ,IAAI,CAACC,iBAAiB;QACtB,IAAI,CAACO,aAAa,GAAGC;IACvB;IACA,MAAMC,MAAMC,IAAY,EAAiB;QACvC,IAAI,IAAI,CAACH,aAAa,EAAE,MAAM,IAAI,CAACA,aAAa;QAEhD,IAAI,CAACR,IAAI,IAAIW,KAAK9B,MAAM;QACxB,IAAI,IAAI,CAACmB,IAAI,GAAG,IAAI,CAACD,SAAS,EAAE;YAC9B,MAAO,CAAA,IAAI,CAACS,aAAa,GAAG,IAAI,CAACL,MAAM,EAAC;QAC1C;QAEA,IAAI,CAAC,IAAI,CAACb,WAAW,CAACoB,KAAK,CAACC,MAAM,SAAS;YACzC,IAAI,IAAI,CAACC,YAAY,KAAKH,WAAW;gBACnC,IAAI,CAACG,YAAY,GAAG,IAAIjC,QAAc,CAACkC,SAASC;oBAC9C,IAAI,CAACxB,WAAW,CAACyB,IAAI,CAAC,SAAS;wBAC7B,IAAI,CAACH,YAAY,GAAGH;wBACpBI;oBACF;gBACF;YACF;YACA,MAAM,IAAI,CAACD,YAAY;QACzB;IACF;IAEAR,MAAqB;QACnB,OAAO,IAAIzB,QAAQ,CAACkC;YAClB,IAAI,CAACvB,WAAW,CAACc,GAAG,CAACS;QACvB;IACF;AACF;AAEA,MAAMG,oBAAoB,CAACjC;IACzB,MAAMkC,UAAUC,oBAAY,CAACC,GAAG,CAAC;IACjC,MAAMC,QAAQF,oBAAY,CAACC,GAAG,CAAC;IAC/B,IAAI,CAACF,WAAW,CAACG,OAAO;QACtB;IACF;IAEA,IAAI,CAAC7B,SAAS;QACZA,UAAU8B,QAAQC,GAAG,CAACC,QAAQ,IAAIC,IAAAA,mBAAW,EAAC,GAAGC,QAAQ,CAAC;IAC5D;IAEA,IAAI,CAACjC,OAAO;QACVA,QAAQvB,QAAQ,OAAOM;YACrB,IAAI,CAACe,aAAa;gBAChB,MAAMY,WAAE,CAACwB,QAAQ,CAACC,KAAK,CAACV,SAAS;oBAAEW,WAAW;gBAAK;gBACnD,MAAM9B,OAAO+B,aAAI,CAACC,IAAI,CAACb,SAAS;gBAChC3B,cAAc,IAAIM,oBAChBE,MACA,0DAA0D;gBAC1DsB,UAAUW,mCAAwB,GAAG,WAAWC;YAEpD;YACA,MAAMC,aAAaC,KAAKC,SAAS,CAAC5D;YAClC,IAAI;gBACF,MAAMe,YAAYoB,KAAK,CAACuB,aAAa;YACvC,EAAE,OAAO3B,KAAK;gBACZ8B,QAAQC,GAAG,CAAC/B;YACd;QACF;IACF;IAEAd,MAAMV,MAAM,CAAC;QACX,GAAGC,KAAK;QACRQ;IACF;AACF;MAEA,WAAe;IACbb,UAAU,CAAC4D,OACT9C,QACIA,MAAMd,QAAQ,GAAGU,IAAI,CAAC;YACpB,MAAMgC,QAAQF,oBAAY,CAACC,GAAG,CAAC;YAC/B,4DAA4D;YAC5D,IAAImB,CAAAA,wBAAAA,KAAMlC,GAAG,KAAIgB,UAAUW,mCAAwB,EAAE;gBACnD,OAAOzC,YAAYc,GAAG;YACxB;QACF,KACAK;IACN3B,QAAQkC;AACV"}