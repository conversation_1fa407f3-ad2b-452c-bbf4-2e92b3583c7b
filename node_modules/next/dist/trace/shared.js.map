{"version": 3, "sources": ["../../src/trace/shared.ts"], "sourcesContent": ["let _traceGlobals: Map<any, any> = (global as any)._traceGlobals\n\nif (!_traceGlobals) {\n  _traceGlobals = new Map()\n}\n;(global as any)._traceGlobals = _traceGlobals\n\nexport const traceGlobals: Map<any, any> = _traceGlobals\nexport const setGlobal = (key: any, val: any) => {\n  traceGlobals.set(key, val)\n}\n"], "names": ["setGlobal", "traceGlobals", "_traceGlobals", "global", "Map", "key", "val", "set"], "mappings": ";;;;;;;;;;;;;;;IAQaA,SAAS;eAATA;;IADAC,YAAY;eAAZA;;;AAPb,IAAIC,gBAA+B,AAACC,OAAeD,aAAa;AAEhE,IAAI,CAACA,eAAe;IAClBA,gBAAgB,IAAIE;AACtB;AACED,OAAeD,aAAa,GAAGA;AAE1B,MAAMD,eAA8BC;AACpC,MAAMF,YAAY,CAACK,KAAUC;IAClCL,aAAaM,GAAG,CAACF,KAAKC;AACxB"}