{"version": 3, "sources": ["../../src/trace/trace-uploader.ts"], "sourcesContent": ["import findUp from 'next/dist/compiled/find-up'\nimport fsPromise from 'fs/promises'\nimport child_process from 'child_process'\nimport assert from 'assert'\nimport fetch from 'next/dist/compiled/node-fetch'\nimport os from 'os'\nimport { createInterface } from 'readline'\nimport { createReadStream } from 'fs'\nimport path from 'path'\nimport { Telemetry } from '../telemetry/storage'\n\nconst COMMON_ALLOWED_EVENTS = ['memory-usage']\n\n// Predefined set of the event names to be included in the trace.\n// If the trace span's name matches to one of the event names in the set,\n// it'll up uploaded to the trace server.\nconst DEV_ALLOWED_EVENTS = new Set([\n  ...COMMON_ALLOWED_EVENTS,\n  'client-hmr-latency',\n  'hot-reloader',\n  'webpack-invalid-client',\n  'webpack-invalidated-server',\n  'navigation-to-hydration',\n  'start-dev-server',\n  'compile-path',\n  'memory-usage',\n  'server-restart-close-to-memory-threshold',\n])\n\nconst BUILD_ALLOWED_EVENTS = new Set([\n  ...COMMON_ALLOWED_EVENTS,\n  'next-build',\n  'webpack-compilation',\n  'run-webpack-compiler',\n  'create-entrypoints',\n  'worker-main-edge-server',\n  'worker-main-client',\n  'worker-main-server',\n  'server',\n  'make',\n  'seal',\n  'chunk-graph',\n  'optimize-modules',\n  'optimize-chunks',\n  'optimize',\n  'optimize-tree',\n  'optimize-chunk-modules',\n  'module-hash',\n  'client',\n  'static-check',\n  'node-file-trace-build',\n  'static-generation',\n  'next-export',\n  'verify-typescript-setup',\n  'verify-and-lint',\n])\n\nconst {\n  NEXT_TRACE_UPLOAD_DEBUG,\n  // An external env to allow to upload full trace without picking up the relavant spans.\n  // This is mainly for the debugging purpose, to allwo manual audit for full trace for the given build.\n  // [NOTE] This may fail if build is large and generated trace is excessively large.\n  NEXT_TRACE_UPLOAD_FULL,\n} = process.env\n\nconst isDebugEnabled = !!NEXT_TRACE_UPLOAD_DEBUG || !!NEXT_TRACE_UPLOAD_FULL\nconst shouldUploadFullTrace = !!NEXT_TRACE_UPLOAD_FULL\n\nconst [, , traceUploadUrl, mode, projectDir, distDir] = process.argv\n\ntype TraceRequestBody = {\n  metadata: TraceMetadata\n  traces: TraceEvent[][]\n}\n\ninterface TraceEvent {\n  traceId: string\n  parentId?: number\n  name: string\n  id: number\n  startTime: number\n  timestamp: number\n  duration: number\n  tags: Record<string, unknown>\n}\n\ninterface TraceMetadata {\n  anonymousId: string\n  arch: string\n  commit: string\n  cpus: number\n  isTurboSession: boolean\n  mode: string\n  nextVersion: string\n  pkgName: string\n  platform: string\n  sessionId: string\n}\n\n;(async function upload() {\n  const nextVersion = JSON.parse(\n    await fsPromise.readFile(\n      path.resolve(__dirname, '../../package.json'),\n      'utf8'\n    )\n  ).version\n\n  const telemetry = new Telemetry({ distDir })\n\n  const projectPkgJsonPath = await findUp('package.json')\n  assert(projectPkgJsonPath)\n\n  const projectPkgJson = JSON.parse(\n    await fsPromise.readFile(projectPkgJsonPath, 'utf-8')\n  )\n  const pkgName = projectPkgJson.name\n\n  const commit = child_process\n    .spawnSync(\n      os.platform() === 'win32' ? 'git.exe' : 'git',\n      ['rev-parse', 'HEAD'],\n      { shell: true }\n    )\n    .stdout.toString()\n    .trimEnd()\n\n  const readLineInterface = createInterface({\n    input: createReadStream(path.join(projectDir, distDir, 'trace')),\n    crlfDelay: Infinity,\n  })\n\n  let isTurboSession = false\n  const traces = new Map<string, TraceEvent[]>()\n  for await (const line of readLineInterface) {\n    const lineEvents: TraceEvent[] = JSON.parse(line)\n    for (const event of lineEvents) {\n      if (\n        // Always include root spans\n        event.parentId === undefined ||\n        shouldUploadFullTrace ||\n        (mode === 'dev'\n          ? DEV_ALLOWED_EVENTS.has(event.name)\n          : BUILD_ALLOWED_EVENTS.has(event.name))\n      ) {\n        let trace = traces.get(event.traceId)\n        if (trace === undefined) {\n          trace = []\n          traces.set(event.traceId, trace)\n        }\n        if (typeof event.tags.isTurbopack === 'boolean') {\n          isTurboSession = event.tags.isTurbopack\n        }\n        trace.push(event)\n      }\n    }\n  }\n\n  const body: TraceRequestBody = {\n    metadata: {\n      anonymousId: telemetry.anonymousId,\n      arch: os.arch(),\n      commit,\n      cpus: os.cpus().length,\n      isTurboSession,\n      mode,\n      nextVersion,\n      pkgName,\n      platform: os.platform(),\n      sessionId: telemetry.sessionId,\n    },\n    traces: [...traces.values()],\n  }\n\n  if (isDebugEnabled) {\n    console.log('Sending request with body', JSON.stringify(body, null, 2))\n  }\n\n  let res = await fetch(traceUploadUrl, {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json',\n      'x-trace-transfer-mode': shouldUploadFullTrace ? 'full' : 'default',\n    },\n    body: JSON.stringify(body),\n  })\n\n  if (isDebugEnabled) {\n    console.log('Received response', res.status, await res.json())\n  }\n})()\n"], "names": ["COMMON_ALLOWED_EVENTS", "DEV_ALLOWED_EVENTS", "Set", "BUILD_ALLOWED_EVENTS", "NEXT_TRACE_UPLOAD_DEBUG", "NEXT_TRACE_UPLOAD_FULL", "process", "env", "isDebugEnabled", "shouldUploadFullTrace", "traceUploadUrl", "mode", "projectDir", "distDir", "argv", "upload", "nextVersion", "JSON", "parse", "fsPromise", "readFile", "path", "resolve", "__dirname", "version", "telemetry", "Telemetry", "projectPkgJsonPath", "findUp", "assert", "projectPkgJson", "pkgName", "name", "commit", "child_process", "spawnSync", "os", "platform", "shell", "stdout", "toString", "trimEnd", "readLineInterface", "createInterface", "input", "createReadStream", "join", "crlfDelay", "Infinity", "isTurboSession", "traces", "Map", "line", "lineEvents", "event", "parentId", "undefined", "has", "trace", "get", "traceId", "set", "tags", "isTurbopack", "push", "body", "metadata", "anonymousId", "arch", "cpus", "length", "sessionId", "values", "console", "log", "stringify", "res", "fetch", "method", "headers", "status", "json"], "mappings": ";;;;+DAAmB;iEACG;sEACI;+DACP;kEACD;2DACH;0BACiB;oBACC;6DAChB;yBACS;;;;;;AAE1B,MAAMA,wBAAwB;IAAC;CAAe;AAE9C,iEAAiE;AACjE,yEAAyE;AACzE,yCAAyC;AACzC,MAAMC,qBAAqB,IAAIC,IAAI;OAC9BF;IACH;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAMG,uBAAuB,IAAID,IAAI;OAChCF;IACH;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,EACJI,uBAAuB,EACvB,uFAAuF;AACvF,sGAAsG;AACtG,mFAAmF;AACnFC,sBAAsB,EACvB,GAAGC,QAAQC,GAAG;AAEf,MAAMC,iBAAiB,CAAC,CAACJ,2BAA2B,CAAC,CAACC;AACtD,MAAMI,wBAAwB,CAAC,CAACJ;AAEhC,MAAM,KAAKK,gBAAgBC,MAAMC,YAAYC,QAAQ,GAAGP,QAAQQ,IAAI;AA+BlE,CAAA,eAAeC;IACf,MAAMC,cAAcC,KAAKC,KAAK,CAC5B,MAAMC,iBAAS,CAACC,QAAQ,CACtBC,aAAI,CAACC,OAAO,CAACC,WAAW,uBACxB,SAEFC,OAAO;IAET,MAAMC,YAAY,IAAIC,kBAAS,CAAC;QAAEb;IAAQ;IAE1C,MAAMc,qBAAqB,MAAMC,IAAAA,eAAM,EAAC;IACxCC,IAAAA,eAAM,EAACF;IAEP,MAAMG,iBAAiBb,KAAKC,KAAK,CAC/B,MAAMC,iBAAS,CAACC,QAAQ,CAACO,oBAAoB;IAE/C,MAAMI,UAAUD,eAAeE,IAAI;IAEnC,MAAMC,SAASC,sBAAa,CACzBC,SAAS,CACRC,WAAE,CAACC,QAAQ,OAAO,UAAU,YAAY,OACxC;QAAC;QAAa;KAAO,EACrB;QAAEC,OAAO;IAAK,GAEfC,MAAM,CAACC,QAAQ,GACfC,OAAO;IAEV,MAAMC,oBAAoBC,IAAAA,yBAAe,EAAC;QACxCC,OAAOC,IAAAA,oBAAgB,EAACxB,aAAI,CAACyB,IAAI,CAAClC,YAAYC,SAAS;QACvDkC,WAAWC;IACb;IAEA,IAAIC,iBAAiB;IACrB,MAAMC,SAAS,IAAIC;IACnB,WAAW,MAAMC,QAAQV,kBAAmB;QAC1C,MAAMW,aAA2BpC,KAAKC,KAAK,CAACkC;QAC5C,KAAK,MAAME,SAASD,WAAY;YAC9B,IACE,4BAA4B;YAC5BC,MAAMC,QAAQ,KAAKC,aACnB/C,yBACCE,CAAAA,SAAS,QACNV,mBAAmBwD,GAAG,CAACH,MAAMtB,IAAI,IACjC7B,qBAAqBsD,GAAG,CAACH,MAAMtB,IAAI,CAAA,GACvC;gBACA,IAAI0B,QAAQR,OAAOS,GAAG,CAACL,MAAMM,OAAO;gBACpC,IAAIF,UAAUF,WAAW;oBACvBE,QAAQ,EAAE;oBACVR,OAAOW,GAAG,CAACP,MAAMM,OAAO,EAAEF;gBAC5B;gBACA,IAAI,OAAOJ,MAAMQ,IAAI,CAACC,WAAW,KAAK,WAAW;oBAC/Cd,iBAAiBK,MAAMQ,IAAI,CAACC,WAAW;gBACzC;gBACAL,MAAMM,IAAI,CAACV;YACb;QACF;IACF;IAEA,MAAMW,OAAyB;QAC7BC,UAAU;YACRC,aAAa1C,UAAU0C,WAAW;YAClCC,MAAMhC,WAAE,CAACgC,IAAI;YACbnC;YACAoC,MAAMjC,WAAE,CAACiC,IAAI,GAAGC,MAAM;YACtBrB;YACAtC;YACAK;YACAe;YACAM,UAAUD,WAAE,CAACC,QAAQ;YACrBkC,WAAW9C,UAAU8C,SAAS;QAChC;QACArB,QAAQ;eAAIA,OAAOsB,MAAM;SAAG;IAC9B;IAEA,IAAIhE,gBAAgB;QAClBiE,QAAQC,GAAG,CAAC,6BAA6BzD,KAAK0D,SAAS,CAACV,MAAM,MAAM;IACtE;IAEA,IAAIW,MAAM,MAAMC,IAAAA,kBAAK,EAACnE,gBAAgB;QACpCoE,QAAQ;QACRC,SAAS;YACP,gBAAgB;YAChB,yBAAyBtE,wBAAwB,SAAS;QAC5D;QACAwD,MAAMhD,KAAK0D,SAAS,CAACV;IACvB;IAEA,IAAIzD,gBAAgB;QAClBiE,QAAQC,GAAG,CAAC,qBAAqBE,IAAII,MAAM,EAAE,MAAMJ,IAAIK,IAAI;IAC7D;AACF,CAAA"}