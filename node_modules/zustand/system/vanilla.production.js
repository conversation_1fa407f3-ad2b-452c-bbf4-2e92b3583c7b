System.register([],function(f){"use strict";return{execute:function(){f("default",a);function a(b){let t;const c=new Set,u=(e,s)=>{const n=typeof e=="function"?e(t):e;if(n!==t){const r=t;t=s?n:Object.assign({},t,n),c.forEach(o=>o(t,r))}},i=()=>t,S=(e,s=i,n=Object.is)=>{console.warn("[DEPRECATED] Please use `subscribeWithSelector` middleware");let r=s(t);function o(){const l=s(t);if(!n(r,l)){const E=r;e(r=l,E)}}return c.add(o),()=>c.delete(o)},d={setState:u,getState:i,subscribe:(e,s,n)=>s||n?S(e,s,n):(c.add(e),()=>c.delete(e)),destroy:()=>c.clear()};return t=b(u,i,d),d}}}});
